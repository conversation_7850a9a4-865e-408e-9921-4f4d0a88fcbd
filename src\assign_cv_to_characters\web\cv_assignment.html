<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>角色CV分配工具</title>
    <link rel="stylesheet" href="css/style.css">
    <style>
        /* 专用样式 - 使用现代化设计变量 */
        .form-group {
            margin-bottom: var(--spacing-md);
        }

        .form-group label {
            display: block;
            margin-bottom: var(--spacing-sm);
            font-weight: 600;
            color: var(--text-primary);
            font-size: 0.875rem;
        }

        .form-group input, .form-group select {
            width: 100%;
            padding: var(--spacing-sm) var(--spacing-md);
            border: 1px solid var(--border-medium);
            border-radius: var(--radius-md);
            font-size: 0.875rem;
            background: var(--bg-primary);
            color: var(--text-primary);
            transition: border-color var(--transition-fast);
        }

        .form-group input:focus, .form-group select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgb(102 126 234 / 0.1);
        }

        .btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--radius-button);
            cursor: pointer;
            margin-right: var(--spacing-sm);
            font-weight: 600;
            font-size: 0.875rem;
            transition: all var(--transition-smooth);
            position: relative;
            overflow: hidden;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: left 0.5s ease;
        }

        .btn:hover::before {
            left: 100%;
        }
        }

        .btn-warning {
            background: var(--warning-color);
            color: var(--text-inverse);
            box-shadow: 0 4px 15px rgba(217, 119, 6, 0.2);
        }

        .btn-warning:hover {
            background: #b45309;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(217, 119, 6, 0.3);
        }

        .tabs {
            display: flex;
            border-bottom: 1px solid var(--border-glass);
            margin-bottom: var(--spacing-lg);
            background: var(--bg-glass);
            border-radius: var(--radius-lg) var(--radius-lg) 0 0;
            padding: 0 var(--spacing-md);
        }

        .tab {
            padding: var(--spacing-md) var(--spacing-lg);
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all var(--transition-smooth);
            font-weight: 500;
            color: var(--text-secondary);
            position: relative;
        }

        .tab:hover {
            color: var(--primary-color);
            background: rgba(102, 126, 234, 0.05);
        }

        .tab.active {
            border-bottom-color: var(--primary-color);
            color: var(--primary-color);
            background: rgba(102, 126, 234, 0.1);
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: var(--bg-tertiary);
            border-radius: var(--radius-lg);
            overflow: hidden;
            margin: var(--spacing-sm) 0;
            box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .progress-fill {
            height: 100%;
            background: var(--primary-gradient);
            transition: width 0.3s ease;
            position: relative;
        }

        .progress-fill::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            animation: shimmer 2s infinite;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        /* ==================== 操作日志页面专用样式 ==================== */
        .logs-panel {
            display: flex;
            flex-direction: column;
            height: 100%;
            overflow: hidden; /* 确保面板不会产生滚动条 */
            box-sizing: border-box;
        }

        /* 操作日志页面头部样式 */
        .logs-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--spacing-lg);
            padding-bottom: var(--spacing-md);
            border-bottom: 1px solid var(--border-glass);
            flex-shrink: 0;
        }

        .logs-header h3 {
            margin: 0;
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--text-primary);
        }

        .logs-actions {
            display: flex;
            align-items: center;
        }

        /* 操作日志容器样式 */
        .logs-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            min-height: 0; /* 重要：允许flex子项收缩 */
            position: relative;
            overflow: hidden; /* 确保容器不会产生滚动条 */
            width: 100%; /* 确保宽度自适应 */
        }

        .logs-content-wrapper {
            flex: 1;
            overflow: hidden;
            border: 1px solid var(--border-glass);
            border-radius: var(--radius-lg);
            background: var(--bg-glass);
            display: flex;
            flex-direction: column;
            min-height: 0; /* 确保可以收缩 */
            width: 100%; /* 确保宽度自适应 */
            box-sizing: border-box;
        }

        /* 优化后的日志面板样式 */
        .log-panel {
            flex: 1;
            overflow-y: auto;
            overflow-x: hidden;
            padding: var(--spacing-md);
            font-family: var(--font-mono);
            font-size: 0.75rem;
            line-height: 1.4;
            background: transparent;
            border: none;
            border-radius: 0;
            box-shadow: none;
            /* 自定义滚动条样式 */
            scrollbar-width: thin;
            scrollbar-color: rgba(255, 255, 255, 0.3) transparent;
        }

        /* Webkit浏览器滚动条样式 */
        .log-panel::-webkit-scrollbar {
            width: 8px;
        }

        .log-panel::-webkit-scrollbar-track {
            background: transparent;
        }

        .log-panel::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 4px;
            transition: background var(--transition-smooth);
        }

        .log-panel::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.5);
        }

        /* 日志条目样式 */
        .log-panel div {
            margin-bottom: var(--spacing-xs);
            padding: var(--spacing-xs) 0;
            color: var(--text-secondary);
            word-wrap: break-word;
            white-space: pre-wrap;
        }

        .log-panel p {
            margin: 0;
            padding: var(--spacing-xs) 0;
            color: var(--text-secondary);
        }

        /* 操作日志响应式设计 */
        @media (max-width: 768px) {
            .logs-header {
                flex-direction: column;
                gap: var(--spacing-sm);
                align-items: flex-start;
            }

            .logs-actions {
                width: 100%;
                justify-content: flex-end;
            }

            .logs-panel {
                padding: var(--spacing-md);
            }

            .log-panel {
                font-size: 0.7rem;
                padding: var(--spacing-sm);
            }
        }

        @media (max-width: 480px) {
            .logs-panel {
                padding: var(--spacing-sm);
                height: 100%; /* 确保移动端高度自适应 */
            }

            .logs-header h3 {
                font-size: 1.1rem;
            }

            .logs-actions {
                justify-content: center;
            }

            .logs-container {
                flex: 1;
                min-height: 0;
            }

            .log-panel {
                font-size: 0.65rem;
                padding: var(--spacing-xs);
            }
        }

        /* ==================== CV简名管理页面专用样式 ==================== */
        .nicknames-panel {
            display: flex;
            flex-direction: column;
            height: 100%;
            overflow: hidden;
            box-sizing: border-box;
        }

        .nicknames-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--spacing-lg);
            padding-bottom: var(--spacing-md);
            border-bottom: 1px solid var(--border-glass);
            flex-shrink: 0;
        }

        .nicknames-header h3 {
            margin: 0;
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--text-primary);
        }

        .nicknames-actions {
            display: flex;
            align-items: center;
        }

        .nicknames-input-section {
            flex-shrink: 0;
            margin-bottom: var(--spacing-lg);
            padding: var(--spacing-md);
            background: var(--bg-glass);
            border: 1px solid var(--border-glass);
            border-radius: var(--radius-lg);
        }

        .nicknames-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            min-height: 0;
            overflow: hidden;
            width: 100%;
        }

        .nicknames-content-wrapper {
            flex: 1;
            overflow-y: auto;
            overflow-x: hidden;
            border: 1px solid var(--border-glass);
            border-radius: var(--radius-lg);
            background: var(--bg-glass);
            padding: var(--spacing-md);
            scrollbar-width: thin;
            scrollbar-color: rgba(255, 255, 255, 0.3) transparent;
        }

        .nicknames-content-wrapper::-webkit-scrollbar {
            width: 8px;
        }

        .nicknames-content-wrapper::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 4px;
        }

        /* ==================== 分配任务页面专用样式 ==================== */
        .assignment-panel {
            display: flex;
            flex-direction: column;
            height: 100%;
            overflow: hidden;
            box-sizing: border-box;
        }

        .assignment-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--spacing-lg);
            padding-bottom: var(--spacing-md);
            border-bottom: 1px solid var(--border-glass);
            flex-shrink: 0;
        }

        .assignment-header h3 {
            margin: 0;
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--text-primary);
        }

        .assignment-config-section {
            flex-shrink: 0;
            margin-bottom: var(--spacing-lg);
            padding: var(--spacing-md);
            background: var(--bg-glass);
            border: 1px solid var(--border-glass);
            border-radius: var(--radius-lg);
        }

        .assignment-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            min-height: 0;
            overflow: hidden;
            width: 100%;
        }

        .assignment-content-wrapper {
            flex: 1;
            overflow-y: auto;
            overflow-x: hidden;
            border: 1px solid var(--border-glass);
            border-radius: var(--radius-lg);
            background: var(--bg-glass);
            padding: var(--spacing-md);
            scrollbar-width: thin;
            scrollbar-color: rgba(255, 255, 255, 0.3) transparent;
        }

        .assignment-content-wrapper::-webkit-scrollbar {
            width: 8px;
        }

        .assignment-content-wrapper::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 4px;
        }

        .mapping-table {
            width: 100%;
            border-collapse: collapse;
            background: var(--bg-glass);
            border-radius: var(--radius-lg);
            overflow: hidden;
            box-shadow: var(--shadow-md);
        }

        .mapping-table th, .mapping-table td {
            border: 1px solid var(--border-glass);
            padding: var(--spacing-sm) var(--spacing-md);
            text-align: left;
            transition: background-color var(--transition-fast);
        }

        .mapping-table th {
            background: var(--bg-glass-secondary);
            font-weight: 600;
            color: var(--text-primary);
            font-size: 0.875rem;
        }

        .mapping-table tr:hover td {
            background: var(--bg-glass-hover);
        }

        .file-upload {
            border: 2px dashed var(--border-medium);
            border-radius: var(--radius-lg);
            padding: var(--spacing-2xl);
            text-align: center;
            cursor: pointer;
            transition: all var(--transition-smooth);
            background: var(--bg-glass);
            backdrop-filter: var(--glass-blur-light);
            position: relative;
            overflow: hidden;
        }

        .file-upload::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.1), transparent);
            transition: left 0.8s ease;
        }

        .file-upload:hover {
            border-color: var(--primary-color);
            background: var(--bg-glass-hover);
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }

        .file-upload:hover::before {
            left: 100%;
        }

        .file-upload.dragover {
            border-color: var(--primary-color);
            background: var(--bg-glass-hover);
            transform: translateY(-4px);
            box-shadow: var(--shadow-lg);
        }

        .hidden {
            display: none !important;
        }

        .alert {
            padding: var(--spacing-md);
            border-radius: var(--radius-lg);
            margin: var(--spacing-md) 0;
            backdrop-filter: var(--glass-blur-light);
            border-left: 4px solid;
            transition: all var(--transition-smooth);
        }

        .alert:hover {
            transform: translateX(5px);
            box-shadow: var(--shadow-sm);
        }

        .alert-success {
            background: rgba(212, 237, 218, 0.9);
            color: #155724;
            border-color: var(--success-color);
            border-left-color: var(--success-color);
        }

        .alert-error {
            background: rgba(248, 215, 218, 0.9);
            color: #721c24;
            border-color: var(--error-color);
            border-left-color: var(--error-color);
        }

        .alert-warning {
            background: rgba(255, 243, 205, 0.9);
            color: #856404;
            border-color: var(--warning-color);
            border-left-color: var(--warning-color);
        }

        /* 设置页面样式 - 提高优先级，仅在active状态下生效 */
        #settingsTab.tab-content.active .config-panel .settings-section {
            margin-bottom: var(--spacing-lg) !important;
            padding: var(--spacing-md) !important;
            background: var(--bg-glass) !important;
            backdrop-filter: var(--glass-blur) !important;
            border: 1px solid var(--border-glass) !important;
            border-radius: var(--radius-glass) !important;
            box-shadow: var(--shadow-md) !important;
            transition: all var(--transition-smooth) !important;
            /* 确保在绝对定位容器中正确显示 */
            position: relative !important;
            z-index: 1 !important;
            width: 100% !important;
            box-sizing: border-box !important;
        }

        #settingsTab.tab-content.active .config-panel .settings-section:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        #settingsTab.tab-content.active .config-panel .settings-section h4 {
            margin: 0 0 var(--spacing-md) 0;
            color: var(--text-primary);
            font-size: 1rem;
            font-weight: 700;
            border-bottom: 2px solid var(--primary-color);
            padding-bottom: var(--spacing-sm);
            position: relative;
        }

        #settingsTab.tab-content.active .config-panel .settings-section h4::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 30%;
            height: 2px;
            background: var(--primary-gradient);
        }

        #settingsTab.tab-content.active .config-panel .settings-section .form-group {
            margin-bottom: var(--spacing-md);
        }

        #settingsTab.tab-content.active .config-panel .settings-section .form-group:last-child {
            margin-bottom: 0;
        }

        #settingsTab.tab-content.active .config-panel .settings-section input[type="text"],
        #settingsTab.tab-content.active .config-panel .settings-section input[type="password"],
        #settingsTab.tab-content.active .config-panel .settings-section input[type="number"] {
            width: 300px !important;
            margin-right: var(--spacing-sm) !important;
            background: var(--bg-glass) !important;
            backdrop-filter: var(--glass-blur-light) !important;
            border: 1px solid var(--border-glass) !important;
            transition: all var(--transition-smooth) !important;
            /* 确保在绝对定位容器中正确显示 */
            position: relative !important;
            z-index: 1 !important;
            padding: var(--spacing-sm) !important;
            border-radius: var(--radius-md) !important;
            color: var(--text-primary) !important;
        }

        #settingsTab.tab-content.active .config-panel .settings-section input[type="text"]:focus,
        #settingsTab.tab-content.active .config-panel .settings-section input[type="password"]:focus,
        #settingsTab.tab-content.active .config-panel .settings-section input[type="number"]:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
            background: var(--bg-glass-hover);
        }

        #settingsTab.tab-content.active .config-panel .settings-section small {
            display: block;
            margin-top: var(--spacing-xs);
            font-size: 0.75rem;
            color: var(--text-secondary);
            font-style: italic;
        }

        /* 表单行样式 */
        .form-row {
            display: flex;
            flex-wrap: wrap;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-md);
        }

        .form-row .form-group {
            flex: 1;
            min-width: 200px;
            margin-bottom: 0;
        }

        .form-row .form-group input,
        .form-row .form-group select {
            width: 100%;
        }

        /* 设置页面头部样式 - 仅限基础设置页面且在active状态 */
        #settingsTab.tab-content.active .settings-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--spacing-lg);
            padding-bottom: var(--spacing-md);
            border-bottom: 1px solid var(--border-glass);
        }

        #settingsTab.tab-content.active .settings-header h3 {
            margin: 0;
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--text-primary);
        }

        #settingsTab.tab-content.active .settings-actions {
            display: flex;
            gap: var(--spacing-sm);
        }

        #settingsTab.tab-content.active .settings-actions .btn {
            margin-right: 0;
        }

        /* 设置TAB导航样式 - 仅限基础设置页面且在active状态 */
        #settingsTab.tab-content.active .settings-tabs {
            display: flex;
            background: var(--bg-glass);
            border-radius: var(--radius-lg);
            padding: var(--spacing-xs);
            margin-bottom: var(--spacing-lg);
            border: 1px solid var(--border-glass);
            box-shadow: var(--shadow-sm);
        }

        #settingsTab.tab-content.active .settings-tab {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: var(--spacing-sm);
            padding: var(--spacing-md) var(--spacing-lg);
            cursor: pointer;
            border-radius: var(--radius-md);
            transition: all var(--transition-smooth);
            font-weight: 500;
            color: var(--text-secondary);
            position: relative;
            background: transparent;
        }

        #settingsTab.tab-content.active .settings-tab:hover {
            color: var(--primary-color);
            background: rgba(102, 126, 234, 0.05);
            transform: translateY(-1px);
        }

        #settingsTab.tab-content.active .settings-tab.active {
            color: var(--text-inverse);
            background: var(--primary-gradient);
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
            transform: translateY(-2px);
        }

        #settingsTab.tab-content.active .settings-tab .tab-icon {
            font-size: 1.1rem;
        }

        #settingsTab.tab-content.active .settings-tab .tab-text {
            font-size: 0.875rem;
            font-weight: 600;
        }

        /* 修复外部CSS冲突 - 确保基础设置页面的tab-content正确工作 */
        #settingsTab.tab-content.active {
            flex: 1 !important;
            display: flex !important;
            flex-direction: column !important;
            min-height: 0 !important;
            overflow: hidden !important;
            height: 100% !important; /* 确保高度传递 */
        }

        /* 设置TAB内容区域样式 - 提高优先级，仅在active状态下生效 */
        #settingsTab.tab-content.active .config-panel {
            flex: 1 !important;
            display: flex !important;
            flex-direction: column !important;
            min-height: 0 !important;
            overflow: hidden !important;
            height: 100% !important; /* 确保高度传递 */
        }

        #settingsTab.tab-content.active .config-panel .settings-tab-content {
            position: relative !important;
            flex: 1 !important;
            display: flex !important;
            flex-direction: column !important;
            min-height: 0 !important; /* 允许flex收缩 */
            overflow: hidden !important; /* 确保容器不会因内容变化而改变大小 */
        }

        #settingsTab.tab-content.active .config-panel .settings-tab-pane {
            position: absolute !important;
            top: 0 !important;
            left: 0 !important;
            right: 0 !important;
            bottom: 0 !important;
            opacity: 0 !important;
            visibility: hidden !important;
            z-index: 1 !important; /* 确保面板在正确的层级 */
            transition: opacity 0.3s ease-in-out, visibility 0.3s ease-in-out !important;
            overflow-y: auto !important;
            overflow-x: hidden !important;
            padding: var(--spacing-md) !important;
            box-sizing: border-box !important;
            /* 自定义滚动条样式 */
            scrollbar-width: thin;
            scrollbar-color: rgba(255, 255, 255, 0.3) transparent;
            /* 确保内容可见性 */
            background: transparent !important;
            color: inherit !important;
            /* 确保高度计算正确 */
            height: 100% !important;
            width: 100% !important;
        }

        #settingsTab.tab-content.active .config-panel .settings-tab-pane.active {
            opacity: 1 !important;
            visibility: visible !important;
            z-index: 2 !important; /* 激活的面板在更高层级 */
        }

        /* Webkit浏览器滚动条样式 - 仅限基础设置页面且在active状态 */
        #settingsTab.tab-content.active .config-panel .settings-tab-pane::-webkit-scrollbar {
            width: 8px;
        }

        #settingsTab.tab-content.active .config-panel .settings-tab-pane::-webkit-scrollbar-track {
            background: transparent;
        }

        #settingsTab.tab-content.active .config-panel .settings-tab-pane::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 4px;
            transition: background var(--transition-smooth);
        }

        #settingsTab.tab-content.active .config-panel .settings-tab-pane::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.5);
        }

        /* 基础设置页面响应式设计 - 仅限基础设置页面且在active状态 */
        @media (max-width: 768px) {
            #settingsTab.tab-content.active .settings-header {
                flex-direction: column;
                gap: var(--spacing-sm);
                align-items: flex-start;
            }

            #settingsTab.tab-content.active .settings-actions {
                width: 100%;
                justify-content: flex-end;
            }

            #settingsTab.tab-content.active .settings-tabs {
                flex-direction: column;
                gap: var(--spacing-xs);
            }

            #settingsTab.tab-content.active .settings-tab {
                width: 100%;
                justify-content: center;
            }

            #settingsTab.tab-content.active .config-panel .settings-tab-content {
                margin-top: var(--spacing-md);
            }

            #settingsTab.tab-content.active .config-panel .settings-tab-pane {
                padding: var(--spacing-sm);
            }

            #settingsTab.tab-content.active .config-panel .settings-section {
                margin-bottom: var(--spacing-md);
                padding: var(--spacing-sm);
            }

            #settingsTab.tab-content.active .config-panel .settings-section input[type="text"],
            #settingsTab.tab-content.active .config-panel .settings-section input[type="password"],
            #settingsTab.tab-content.active .config-panel .settings-section input[type="number"] {
                width: 100%;
                margin-right: 0;
                margin-bottom: var(--spacing-xs);
            }
        }

        @media (max-width: 480px) {
            #settingsTab.tab-content.active .settings-header h3 {
                font-size: 1.1rem;
            }

            #settingsTab.tab-content.active .settings-actions {
                justify-content: center;
                flex-wrap: wrap;
                gap: var(--spacing-xs);
            }

            #settingsTab.tab-content.active .settings-tab {
                font-size: 0.8rem;
                padding: var(--spacing-xs) var(--spacing-sm);
            }

            #settingsTab.tab-content.active .config-panel .settings-tab-pane {
                padding: var(--spacing-xs);
            }

            #settingsTab.tab-content.active .config-panel .settings-section {
                padding: var(--spacing-xs);
                margin-bottom: var(--spacing-sm);
            }

            #settingsTab.tab-content.active .config-panel .settings-section h4 {
                font-size: 0.9rem;
            }
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* ==================== CV列表页面专用样式 ==================== */
        .cv-list-panel {
            display: flex;
            flex-direction: column;
            height: 100%;
            padding: var(--spacing-lg);
            overflow: hidden; /* 确保面板不会产生滚动条 */
            box-sizing: border-box;
        }

        /* CV列表页面头部样式 */
        .cv-list-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--spacing-lg);
            padding-bottom: var(--spacing-md);
            border-bottom: 1px solid var(--border-glass);
            flex-shrink: 0;
        }

        .cv-list-header h3 {
            margin: 0;
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--text-primary);
        }

        .cv-list-actions {
            display: flex;
            align-items: center;
        }

        /* CV列表容器样式 */
        .cv-list-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            min-height: 0; /* 重要：允许flex子项收缩 */
            position: relative;
            overflow: hidden; /* 确保容器不会产生滚动条 */
            width: 100%; /* 确保宽度自适应 */
        }

        .cv-list-table-wrapper {
            flex: 1;
            overflow: hidden;
            border: 1px solid var(--border-glass);
            border-radius: var(--radius-lg);
            background: var(--bg-glass);
            display: flex;
            flex-direction: column;
            min-height: 0; /* 确保可以收缩 */
            width: 100%; /* 确保宽度自适应 */
            box-sizing: border-box;
        }

        /* 固定表头的表格样式 */
        .cv-list-table-wrapper .mapping-table {
            height: 100%;
            display: flex;
            flex-direction: column;
            margin: 0;
            border-radius: 0;
            border: none;
        }

        .cv-list-table-wrapper .mapping-table thead {
            flex-shrink: 0;
            background: var(--bg-glass-secondary);
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .cv-list-table-wrapper .mapping-table thead th {
            padding: var(--spacing-md);
            font-weight: 600;
            color: var(--text-primary);
            border-bottom: 2px solid var(--border-glass);
            background: var(--bg-glass-secondary);
            position: sticky;
            top: 0;
        }

        .cv-list-table-wrapper .mapping-table tbody {
            flex: 1;
            overflow-y: auto;
            overflow-x: hidden;
            display: block;
            /* 自定义滚动条样式 */
            scrollbar-width: thin;
            scrollbar-color: rgba(255, 255, 255, 0.3) transparent;
        }

        /* Webkit浏览器滚动条样式 */
        .cv-list-table-wrapper .mapping-table tbody::-webkit-scrollbar {
            width: 8px;
        }

        .cv-list-table-wrapper .mapping-table tbody::-webkit-scrollbar-track {
            background: transparent;
        }

        .cv-list-table-wrapper .mapping-table tbody::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 4px;
            transition: background var(--transition-smooth);
        }

        .cv-list-table-wrapper .mapping-table tbody::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.5);
        }

        .cv-list-table-wrapper .mapping-table tbody tr {
            display: table;
            width: 100%;
            table-layout: fixed;
        }

        .cv-list-table-wrapper .mapping-table tbody td {
            padding: var(--spacing-sm) var(--spacing-md);
            border-bottom: 1px solid var(--border-glass);
            vertical-align: middle;
        }

        /* 表格列宽设置 */
        .cv-list-table-wrapper .mapping-table th:nth-child(1),
        .cv-list-table-wrapper .mapping-table td:nth-child(1) {
            width: 8%;
            text-align: center;
        }

        .cv-list-table-wrapper .mapping-table th:nth-child(2),
        .cv-list-table-wrapper .mapping-table td:nth-child(2) {
            width: 25%;
        }

        .cv-list-table-wrapper .mapping-table th:nth-child(3),
        .cv-list-table-wrapper .mapping-table td:nth-child(3) {
            width: 15%;
        }

        .cv-list-table-wrapper .mapping-table th:nth-child(4),
        .cv-list-table-wrapper .mapping-table td:nth-child(4) {
            width: 10%;
            text-align: center;
        }

        .cv-list-table-wrapper .mapping-table th:nth-child(5),
        .cv-list-table-wrapper .mapping-table td:nth-child(5) {
            width: 15%;
            text-align: right;
        }

        .cv-list-table-wrapper .mapping-table th:nth-child(6),
        .cv-list-table-wrapper .mapping-table td:nth-child(6) {
            width: 27%;
        }

        /* 固定分页控件样式 */
        .cv-list-pagination-fixed {
            flex-shrink: 0;
            margin-top: var(--spacing-md);
            padding: var(--spacing-md);
            background: var(--bg-glass);
            border: 1px solid var(--border-glass);
            border-radius: var(--radius-lg);
            position: relative; /* 改为相对定位，在flex布局中更稳定 */
            z-index: 5;
            width: 100%;
            box-sizing: border-box;
        }

        /* 空状态样式 */
        .cv-list-table-wrapper p {
            text-align: center;
            color: var(--text-secondary);
            padding: var(--spacing-2xl);
            margin: 0;
            font-style: italic;
        }

        /* CV列表响应式设计 */
        @media (max-width: 768px) {
            .cv-list-header {
                flex-direction: column;
                gap: var(--spacing-sm);
                align-items: flex-start;
            }

            .cv-list-actions {
                width: 100%;
                justify-content: flex-end;
            }

            /* 移动端表格列宽调整 */
            .cv-list-table-wrapper .mapping-table th:nth-child(1),
            .cv-list-table-wrapper .mapping-table td:nth-child(1) {
                width: 10%;
            }

            .cv-list-table-wrapper .mapping-table th:nth-child(2),
            .cv-list-table-wrapper .mapping-table td:nth-child(2) {
                width: 30%;
            }

            .cv-list-table-wrapper .mapping-table th:nth-child(3),
            .cv-list-table-wrapper .mapping-table td:nth-child(3) {
                display: none; /* 隐藏CV ID列 */
            }

            .cv-list-table-wrapper .mapping-table th:nth-child(4),
            .cv-list-table-wrapper .mapping-table td:nth-child(4) {
                width: 15%;
            }

            .cv-list-table-wrapper .mapping-table th:nth-child(5),
            .cv-list-table-wrapper .mapping-table td:nth-child(5) {
                width: 20%;
            }

            .cv-list-table-wrapper .mapping-table th:nth-child(6),
            .cv-list-table-wrapper .mapping-table td:nth-child(6) {
                width: 25%;
            }
        }

        @media (max-width: 480px) {
            .cv-list-panel {
                padding: var(--spacing-md);
                height: 100%; /* 确保移动端高度自适应 */
            }

            .cv-list-header h3 {
                font-size: 1.1rem;
            }

            .cv-list-actions {
                justify-content: center;
            }

            .cv-list-container {
                flex: 1;
                min-height: 0;
            }

            /* 超小屏幕表格优化 */
            .cv-list-table-wrapper .mapping-table th:nth-child(4),
            .cv-list-table-wrapper .mapping-table td:nth-child(4) {
                display: none; /* 隐藏性别列 */
            }

            .cv-list-table-wrapper .mapping-table th:nth-child(2),
            .cv-list-table-wrapper .mapping-table td:nth-child(2) {
                width: 40%;
            }

            .cv-list-table-wrapper .mapping-table th:nth-child(5),
            .cv-list-table-wrapper .mapping-table td:nth-child(5) {
                width: 25%;
            }

            .cv-list-table-wrapper .mapping-table th:nth-child(6),
            .cv-list-table-wrapper .mapping-table td:nth-child(6) {
                width: 35%;
            }

            .cv-list-pagination-fixed {
                padding: var(--spacing-sm);
            }
        }

        /* ==================== 角色-CV映射表页面专用样式 ==================== */
        .mapping-panel {
            display: flex;
            flex-direction: column;
            height: 100%;
            padding: var(--spacing-md); /* 从1.5rem减少到1rem */
            overflow: hidden; /* 确保面板不会产生滚动条 */
            box-sizing: border-box;
        }

        /* 映射表页面头部样式 */
        .mapping-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--spacing-md); /* 从1.5rem减少到1rem */
            padding-bottom: var(--spacing-sm); /* 从1rem减少到0.5rem */
            border-bottom: 1px solid var(--border-glass);
            flex-shrink: 0;
        }

        .mapping-header h3 {
            margin: 0;
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--text-primary);
        }

        .mapping-actions {
            display: flex;
            align-items: center;
        }

        /* 映射表容器样式 */
        .mapping-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            min-height: 0; /* 重要：允许flex子项收缩 */
            position: relative;
            overflow: hidden; /* 确保容器不会产生滚动条 */
            width: 100%; /* 确保宽度自适应 */
        }

        .mapping-table-wrapper {
            flex: 1;
            overflow: hidden;
            border: 1px solid var(--border-glass);
            border-radius: var(--radius-lg);
            background: var(--bg-glass);
            display: flex;
            flex-direction: column;
            min-height: 0; /* 确保可以收缩 */
            width: 100%; /* 确保宽度自适应 */
            box-sizing: border-box;
        }

        /* 固定表头的映射表格样式 */
        .mapping-table-wrapper .mapping-table {
            height: 100%;
            display: flex;
            flex-direction: column;
            margin: 0;
            border-radius: 0;
            border: none;
        }

        .mapping-table-wrapper .mapping-table thead {
            flex-shrink: 0;
            background: var(--bg-glass-secondary);
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .mapping-table-wrapper .mapping-table thead th {
            padding: var(--spacing-md);
            font-weight: 600;
            color: var(--text-primary);
            border-bottom: 2px solid var(--border-glass);
            background: var(--bg-glass-secondary);
            position: sticky;
            top: 0;
        }

        .mapping-table-wrapper .mapping-table tbody {
            flex: 1;
            overflow-y: auto;
            overflow-x: hidden;
            display: block;
            /* 自定义滚动条样式 */
            scrollbar-width: thin;
            scrollbar-color: rgba(255, 255, 255, 0.3) transparent;
        }

        /* Webkit浏览器滚动条样式 */
        .mapping-table-wrapper .mapping-table tbody::-webkit-scrollbar {
            width: 8px;
        }

        .mapping-table-wrapper .mapping-table tbody::-webkit-scrollbar-track {
            background: transparent;
        }

        .mapping-table-wrapper .mapping-table tbody::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 4px;
            transition: background var(--transition-smooth);
        }

        .mapping-table-wrapper .mapping-table tbody::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.5);
        }

        .mapping-table-wrapper .mapping-table tbody tr {
            display: table;
            width: 100%;
            table-layout: fixed;
        }

        .mapping-table-wrapper .mapping-table tbody td {
            padding: var(--spacing-sm) var(--spacing-md);
            border-bottom: 1px solid var(--border-glass);
            vertical-align: middle;
        }

        /* 映射表格列宽设置 */
        .mapping-table-wrapper .mapping-table th:nth-child(1),
        .mapping-table-wrapper .mapping-table td:nth-child(1) {
            width: 8%;
            text-align: center;
        }

        .mapping-table-wrapper .mapping-table th:nth-child(2),
        .mapping-table-wrapper .mapping-table td:nth-child(2) {
            width: 30%;
        }

        .mapping-table-wrapper .mapping-table th:nth-child(3),
        .mapping-table-wrapper .mapping-table td:nth-child(3) {
            width: 30%;
        }

        .mapping-table-wrapper .mapping-table th:nth-child(4),
        .mapping-table-wrapper .mapping-table td:nth-child(4) {
            width: 15%;
            text-align: right;
        }

        .mapping-table-wrapper .mapping-table th:nth-child(5),
        .mapping-table-wrapper .mapping-table td:nth-child(5) {
            width: 17%;
            text-align: center;
        }

        /* 固定分页控件样式 */
        .mapping-pagination-fixed {
            flex-shrink: 0;
            margin-top: var(--spacing-sm); /* 从1rem减少到0.5rem */
            padding: var(--spacing-sm); /* 从1rem减少到0.5rem */
            background: var(--bg-glass);
            border: 1px solid var(--border-glass);
            border-radius: var(--radius-lg);
            position: relative; /* 改为相对定位，在flex布局中更稳定 */
            z-index: 5;
            width: 100%;
            box-sizing: border-box;
        }

        /* 空状态样式 */
        .mapping-table-wrapper p {
            text-align: center;
            color: var(--text-secondary);
            padding: var(--spacing-2xl);
            margin: 0;
            font-style: italic;
        }

        /* 映射表格样式增强（保持向后兼容） */
        .mapping-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: var(--spacing-md);
            background: var(--bg-glass);
            border-radius: var(--radius-lg);
            overflow: hidden;
            box-shadow: var(--shadow-md);
        }

        .mapping-table th,
        .mapping-table td {
            border: 1px solid var(--border-glass);
            padding: var(--spacing-sm) var(--spacing-md);
            text-align: left;
            transition: background-color var(--transition-fast);
        }

        .mapping-table th {
            background: var(--bg-glass-secondary);
            font-weight: 600;
            color: var(--text-primary);
            font-size: 0.875rem;
        }

        /* 映射表格响应式设计 */
        @media (max-width: 768px) {
            .mapping-header {
                flex-direction: column;
                gap: var(--spacing-sm);
                align-items: flex-start;
            }

            .mapping-actions {
                width: 100%;
                justify-content: flex-end;
            }

            /* 移动端映射表格列宽调整 */
            .mapping-table-wrapper .mapping-table th:nth-child(1),
            .mapping-table-wrapper .mapping-table td:nth-child(1) {
                width: 10%;
            }

            .mapping-table-wrapper .mapping-table th:nth-child(2),
            .mapping-table-wrapper .mapping-table td:nth-child(2) {
                width: 35%;
            }

            .mapping-table-wrapper .mapping-table th:nth-child(3),
            .mapping-table-wrapper .mapping-table td:nth-child(3) {
                width: 35%;
            }

            .mapping-table-wrapper .mapping-table th:nth-child(4),
            .mapping-table-wrapper .mapping-table td:nth-child(4) {
                width: 20%;
            }

            .mapping-table-wrapper .mapping-table th:nth-child(5),
            .mapping-table-wrapper .mapping-table td:nth-child(5) {
                display: none; /* 隐藏操作列 */
            }

            .mapping-pagination-fixed {
                padding: var(--spacing-sm);
            }
        }

        @media (max-width: 480px) {
            .mapping-panel {
                padding: var(--spacing-md);
                height: 100%; /* 确保移动端高度自适应 */
            }

            .mapping-header h3 {
                font-size: 1.1rem;
            }

            .mapping-actions {
                justify-content: center;
            }

            .mapping-container {
                flex: 1;
                min-height: 0;
            }

            /* 超小屏幕映射表格优化 */
            .mapping-table-wrapper .mapping-table th:nth-child(4),
            .mapping-table-wrapper .mapping-table td:nth-child(4) {
                display: none; /* 隐藏价格列 */
            }

            .mapping-table-wrapper .mapping-table th:nth-child(2),
            .mapping-table-wrapper .mapping-table td:nth-child(2) {
                width: 45%;
            }

            .mapping-table-wrapper .mapping-table th:nth-child(3),
            .mapping-table-wrapper .mapping-table td:nth-child(3) {
                width: 45%;
            }

            .mapping-pagination-fixed {
                padding: var(--spacing-sm);
            }
        }

        .mapping-table tr:nth-child(even) {
            background: rgba(248, 250, 252, 0.5);
        }

        .mapping-table tr:hover {
            background: var(--bg-glass-hover);
        }

        .mapping-table tr:hover td {
            background: transparent;
        }
    </style>
</head>
<body>
    <div class="app-container">
        <!-- Header -->
        <header class="app-header">
            <div class="header-content">
                <div class="header-left">
                    <button class="mobile-menu-btn" id="mobileMenuBtn" title="打开菜单">☰</button>
                    <button class="sidebar-toggle-header" id="sidebarToggle" title="展开/折叠菜单">
                        <span class="toggle-icon">◀</span>
                    </button>
                    <div class="header-title-group">
                        <h1>🎭 角色CV分配工具</h1>
                        <p class="subtitle">智能化角色配音分配管理系统</p>
                    </div>
                </div>
                <div class="header-right">
                    <div class="book-selection-header">
                        <label for="bookSelect" class="book-label">书籍:</label>
                        <div class="book-controls-header">
                            <select id="bookSelect" class="book-select-header" onchange="onBookSelectionChange()">
                                <option value="">请先设置API Token</option>
                            </select>
                            <button class="btn-header btn-refresh" onclick="loadBooks()" title="刷新书籍列表">🔄</button>
                        </div>
                    </div>
                    <div class="status-indicator">
                        <span class="status-label">状态:</span>
                        <span class="status-value" id="apiStatus">检查中...</span>
                    </div>
                    <button class="about-btn" onclick="showAboutModal()" title="关于应用">ℹ️</button>
                </div>
            </div>
        </header>

        <!-- 中部区域 - 包含侧边栏和主内容 -->
        <div class="layout-main">
            <!-- Sidebar -->
            <aside class="sidebar" id="sidebar">
            <nav class="sidebar-nav">
                <ul class="nav-list">
                    <li class="nav-item">
                        <a href="#" class="nav-link active" onclick="switchTab('mapping')" data-tab="mapping">
                            <span class="nav-icon">🎭</span>
                            <span class="nav-text">角色-CV映射</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link" onclick="switchTab('cvlist')" data-tab="cvlist">
                            <span class="nav-icon">👥</span>
                            <span class="nav-text">CV列表</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link" onclick="switchTab('nicknames')" data-tab="nicknames">
                            <span class="nav-icon">📝</span>
                            <span class="nav-text">CV简名管理</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link" onclick="switchTab('assignment')" data-tab="assignment">
                            <span class="nav-icon">⚡</span>
                            <span class="nav-text">分配任务</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link" onclick="switchTab('logs')" data-tab="logs">
                            <span class="nav-icon">📋</span>
                            <span class="nav-text">操作日志</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link" onclick="switchTab('settings')" data-tab="settings">
                            <span class="nav-icon">⚙️</span>
                            <span class="nav-text">基础设置</span>
                        </a>
                    </li>
                </ul>
            </nav>
        </aside>

            <!-- Main Content -->
            <main class="main-content">
            <!-- 角色-CV映射标签页 -->
            <div id="mappingTab" class="tab-content active">
                <!-- 映射结果显示区域 -->
                <div class="config-panel mapping-panel">
                    <div class="mapping-header">
                        <h3>🎯 角色-CV映射表</h3>

                        <!-- 动态切换的按钮组 -->
                        <div class="mapping-actions">
                            <!-- 文件选择按钮（初始状态显示） -->
                            <button id="selectFileBtn" class="btn btn-primary" onclick="selectExcelFileDialog()">
                                📂 选择Excel文件
                            </button>

                            <!-- 操作按钮组（解析成功后显示） -->
                            <div id="actionButtonsGroup" class="action-buttons-group hidden">
                                <button class="btn btn-success" onclick="exportMappingData()">
                                    📤 导出映射数据
                                </button>
                                <button class="btn btn-warning" onclick="clearMappingData()">
                                    🗑️ 清空映射数据
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 文件信息显示区域 -->
                    <div id="mappingFileInfo" class="file-info-section hidden">
                        <div class="file-info-content">
                            <span class="file-info-label">已选择文件:</span>
                            <span id="mappingFileName" class="file-name"></span>
                            <span class="file-path">(<span id="mappingFilePath"></span>)</span>
                        </div>
                    </div>

                    <div class="form-group">
                        <span id="mappingStatus" style="color: #666;"></span>
                    </div>

                    <!-- 映射表格容器 -->
                    <div class="mapping-container">
                        <div id="mappingTableContainer" class="mapping-table-wrapper">
                            <p>请先上传并解析Excel文件</p>
                        </div>

                        <!-- 分页控件将通过JavaScript动态插入到这里 -->
                        <div id="mappingPaginationContainer" class="mapping-pagination-fixed"></div>
                    </div>
                </div>
            </div>

            <!-- CV列表标签页 -->
            <div id="cvlistTab" class="tab-content">
                <div class="config-panel cv-list-panel">
                    <!-- CV列表页面头部 -->
                    <div class="cv-list-header">
                        <h3>🎤 CV列表</h3>
                        <div class="cv-list-actions">
                            <button class="btn btn-primary" onclick="loadCvList()">刷新</button>
                        </div>
                    </div>

                    <!-- CV列表表格容器 -->
                    <div class="cv-list-container">
                        <div id="cvListTableContainer" class="cv-list-table-wrapper">
                            <p>请先选择书籍并加载书籍数据</p>
                        </div>

                        <!-- 分页控件将通过JavaScript动态插入到这里 -->
                        <div id="cvListPaginationContainer" class="cv-list-pagination-fixed"></div>
                    </div>
                </div>
            </div>

            <!-- CV简名管理标签页 -->
            <div id="nicknamesTab" class="tab-content">
                <div class="config-panel nicknames-panel">
                    <!-- CV简名管理页面头部 -->
                    <div class="nicknames-header">
                        <h3>📝 CV简名管理</h3>
                        <div class="nicknames-actions">
                            <button class="btn btn-primary" onclick="loadCvNicknames()">刷新列表</button>
                        </div>
                    </div>

                    <!-- CV简名输入区域 -->
                    <div class="nicknames-input-section">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="cvNickname">CV简名:</label>
                                <input type="text" id="cvNickname" placeholder="输入CV简名">
                            </div>
                            <div class="form-group">
                                <label for="cvFullname">CV全名:</label>
                                <input type="text" id="cvFullname" placeholder="输入CV全名">
                            </div>
                        </div>
                        <div class="form-group">
                            <button class="btn btn-success" onclick="addCvNickname()">添加映射</button>
                        </div>
                    </div>

                    <!-- CV简名列表区域 -->
                    <div class="nicknames-container">
                        <div class="nicknames-content-wrapper">
                            <div id="nicknamesTableContainer">
                                <p>加载中...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 分配任务标签页 -->
            <div id="assignmentTab" class="tab-content">
                <div class="config-panel assignment-panel">
                    <!-- 分配任务页面头部 -->
                    <div class="assignment-header">
                        <h3>⚡ 分配任务</h3>
                    </div>

                    <!-- 任务配置区域 -->
                    <div class="assignment-config-section">
                        <div class="form-group">
                            <label>
                                <input type="checkbox" id="enableRealAssignment"> 启用真实分配（取消勾选为测试模式）
                            </label>
                        </div>
                        <div class="form-group">
                            <button class="btn btn-success" onclick="startAssignment()">开始分配</button>
                            <button class="btn btn-warning" onclick="stopAssignment()">停止分配</button>
                        </div>
                        <div id="progressContainer" class="hidden">
                            <h4>分配进度</h4>
                            <div class="progress-bar">
                                <div class="progress-fill" id="progressFill" style="width: 0%"></div>
                            </div>
                            <p id="progressText">准备中...</p>
                        </div>
                    </div>

                    <!-- 分配结果区域 -->
                    <div class="assignment-container">
                        <div class="assignment-content-wrapper">
                            <div id="assignmentResults" class="hidden">
                                <h4>分配结果</h4>
                                <div id="resultsContent"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 操作日志标签页 -->
            <div id="logsTab" class="tab-content">
                <div class="config-panel logs-panel">
                    <!-- 操作日志页面头部 -->
                    <div class="logs-header">
                        <h3>📋 操作日志</h3>
                        <div class="logs-actions">
                            <button class="btn btn-primary" onclick="clearLogs()">清空日志</button>
                        </div>
                    </div>

                    <!-- 操作日志容器 -->
                    <div class="logs-container">
                        <div class="logs-content-wrapper">
                            <div class="log-panel" id="logPanel">
                                <p>系统启动...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 基础设置标签页 -->
            <div id="settingsTab" class="tab-content">
                <div class="config-panel">
                    <!-- 页面标题和操作按钮 -->
                    <div class="settings-header">
                        <h3>⚙️ 基础设置</h3>
                        <div class="settings-actions">
                            <button class="btn btn-success" onclick="saveSettings()">保存设置</button>
                            <button class="btn btn-warning" onclick="resetSettings()">重置默认</button>
                            <button class="btn btn-primary" onclick="loadSettings()">重新加载</button>
                        </div>
                    </div>

                    <!-- 设置TAB导航 -->
                    <div class="settings-tabs">
                        <div class="settings-tab active" onclick="switchSettingsTab('api')" data-tab="api">
                            <span class="tab-icon">🔗</span>
                            <span class="tab-text">API设置</span>
                        </div>
                        <div class="settings-tab" onclick="switchSettingsTab('excel')" data-tab="excel">
                            <span class="tab-icon">📊</span>
                            <span class="tab-text">EXCEL设置</span>
                        </div>
                        <div class="settings-tab" onclick="switchSettingsTab('files')" data-tab="files">
                            <span class="tab-icon">📁</span>
                            <span class="tab-text">文件路径设置</span>
                        </div>
                    </div>

                    <!-- TAB内容区域 -->
                    <div class="settings-tab-content">
                        <!-- API设置TAB -->
                        <div id="apiSettingsTab" class="settings-tab-pane active">
                            <div class="settings-section">
                                <div class="form-group">
                                    <label for="settingsApiToken">API Token:</label>
                                    <input type="password" id="settingsApiToken" placeholder="请输入GStudios API Token">
                                    <button class="btn btn-primary" onclick="applyApiToken()">应用Token</button>
                                </div>
                                <div class="form-group">
                                    <label for="settingsApiBaseUrl">API基础URL:</label>
                                    <input type="text" id="settingsApiBaseUrl" placeholder="API基础URL">
                                </div>
                                <div class="form-group">
                                    <label>
                                        <input type="checkbox" id="settingsEnableRealAssignment"> 启用真实分配模式
                                    </label>
                                    <small style="color: #666; margin-left: 20px;">取消勾选为测试模式</small>
                                </div>
                            </div>
                        </div>

                        <!-- EXCEL设置TAB -->
                        <div id="excelSettingsTab" class="settings-tab-pane">
                            <div class="settings-section">
                                <div class="form-group">
                                    <label for="settingsCharacterKeyword">角色列关键词:</label>
                                    <input type="text" id="settingsCharacterKeyword" placeholder="角色列关键词">
                                </div>
                                <div class="form-group">
                                    <label for="settingsCvKeyword">CV列关键词:</label>
                                    <input type="text" id="settingsCvKeyword" placeholder="CV列关键词">
                                </div>
                                <div class="form-group">
                                    <label for="settingsPriceKeyword">价格列关键词:</label>
                                    <input type="text" id="settingsPriceKeyword" placeholder="价格列关键词">
                                </div>
                                <div class="form-group">
                                    <label for="settingsHeaderRow">表头行号:</label>
                                    <input type="number" id="settingsHeaderRow" placeholder="表头行号" min="1">
                                </div>
                            </div>
                        </div>

                        <!-- 文件路径设置TAB -->
                        <div id="filesSettingsTab" class="settings-tab-pane">
                            <div class="settings-section">
                                <div class="form-group">
                                    <label for="settingsCvConfigFile">CV简名配置文件路径:</label>
                                    <input type="text" id="settingsCvConfigFile" placeholder="CV简名配置文件路径">
                                </div>
                                <div class="form-group">
                                    <label for="settingsLastExcelDir">上次Excel文件目录:</label>
                                    <input type="text" id="settingsLastExcelDir" placeholder="上次Excel文件目录" readonly>
                                    <button class="btn btn-secondary" onclick="clearLastExcelDir()">清空</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
        </div>

        <!-- Mobile Sidebar Overlay -->
        <div class="sidebar-overlay" id="sidebarOverlay"></div>

        <!-- Footer -->
        <footer class="app-footer">
            <div class="footer-content">
                <div class="footer-warning">
                    <div class="footer-icon">⚠️</div>
                    <div class="footer-text">
                        <p>使用前请确保已正确配置API Token和相关设置</p>
                    </div>
                </div>
                <div class="footer-tip">
                    <div class="footer-icon">💡</div>
                    <div class="footer-text">
                        <p>建议在分配前先测试少量角色，确认配置正确后再进行批量操作</p>
                    </div>
                </div>
            </div>
        </footer>
    </div>

    <!-- 关于模态框 -->
    <div class="modal-overlay" id="aboutModal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h2>🎭 关于角色CV分配工具</h2>
                <button class="modal-close" onclick="hideAboutModal()">✕</button>
            </div>
            <div class="modal-body">
                <div class="about-info">
                    <div class="about-section">
                        <h3>📋 应用信息</h3>
                        <p><strong>版本:</strong> v2.0.0</p>
                        <p><strong>功能:</strong> 智能化角色配音分配管理系统</p>
                        <p><strong>特色:</strong> 现代化UI设计，支持批量操作和智能匹配</p>
                    </div>

                    <div class="about-section">
                        <h3>🎨 设计特色</h3>
                        <div class="tech-stack">
                            <span class="tech-item">毛玻璃效果</span>
                            <span class="tech-item">渐变色彩</span>
                            <span class="tech-item">流畅动画</span>
                            <span class="tech-item">响应式设计</span>
                        </div>
                    </div>

                    <div class="about-section">
                        <h3>⚠️ 使用说明</h3>
                        <p><strong>配置要求:</strong> 请确保已正确配置API Token和相关设置</p>
                        <p><strong>操作建议:</strong> 建议先测试少量角色，确认配置正确后再进行批量操作</p>
                        <p><strong>数据安全:</strong> 所有操作均在本地进行，请注意数据备份</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 消息提示 -->
    <div id="messageContainer"></div>

    <script src="js/cv_assignment.js"></script>

    <script>
        // 模态框控制函数
        function showAboutModal() {
            const modal = document.getElementById('aboutModal');
            modal.style.display = 'flex';
            modal.classList.add('show');
        }

        function hideAboutModal() {
            const modal = document.getElementById('aboutModal');
            modal.classList.remove('show');
            setTimeout(() => {
                modal.style.display = 'none';
            }, 300);
        }

        // 点击模态框背景关闭
        document.getElementById('aboutModal').addEventListener('click', function(e) {
            if (e.target === this) {
                hideAboutModal();
            }
        });

        // ESC键关闭模态框
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                hideAboutModal();
            }
        });

        // 侧边栏控制功能
        function initSidebar() {
            const sidebar = document.getElementById('sidebar');
            const sidebarToggle = document.getElementById('sidebarToggle');
            const mobileMenuBtn = document.getElementById('mobileMenuBtn');
            const sidebarOverlay = document.getElementById('sidebarOverlay');
            const mainContent = document.querySelector('.main-content');

            // 检测是否为移动端
            function isMobile() {
                return window.innerWidth <= 768;
            }

            // 从localStorage读取侧边栏状态
            function loadSidebarState() {
                try {
                    const isCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';
                    if (isCollapsed && !isMobile()) {
                        sidebar.classList.add('collapsed');
                        if (mainContent) {
                            mainContent.classList.add('sidebar-collapsed');
                        }
                        console.log('侧边栏状态已恢复：折叠');
                    } else {
                        console.log('侧边栏状态已恢复：展开');
                    }
                } catch (error) {
                    console.warn('读取侧边栏状态失败:', error);
                }
            }

            // 保存侧边栏状态
            function saveSidebarState(collapsed) {
                try {
                    localStorage.setItem('sidebarCollapsed', collapsed);
                    console.log('侧边栏状态已保存:', collapsed ? '折叠' : '展开');
                } catch (error) {
                    console.warn('保存侧边栏状态失败:', error);
                }
            }

            // 初始化侧边栏状态
            loadSidebarState();

            // 桌面端切换侧边栏状态
            sidebarToggle.addEventListener('click', function() {
                if (isMobile()) {
                    // 移动端关闭侧边栏
                    sidebar.classList.remove('mobile-open');
                    if (sidebarOverlay) {
                        sidebarOverlay.classList.remove('show');
                    }
                } else {
                    // 桌面端折叠/展开
                    sidebar.classList.toggle('collapsed');
                    if (mainContent) {
                        mainContent.classList.toggle('sidebar-collapsed');
                    }

                    // 保存状态到localStorage
                    const collapsed = sidebar.classList.contains('collapsed');
                    saveSidebarState(collapsed);

                    // 添加视觉反馈
                    const toggleIcon = sidebarToggle.querySelector('.toggle-icon');
                    if (toggleIcon) {
                        toggleIcon.style.transform = collapsed ? 'rotate(180deg) scale(1.2)' : 'scale(1.2)';
                        setTimeout(() => {
                            toggleIcon.style.transform = collapsed ? 'rotate(180deg)' : '';
                        }, 150);
                    }
                }
            });

            // 移动端菜单按钮
            mobileMenuBtn.addEventListener('click', function() {
                sidebar.classList.add('mobile-open');
                sidebarOverlay.classList.add('show');
            });

            // 点击遮罩层关闭侧边栏
            if (sidebarOverlay) {
                sidebarOverlay.addEventListener('click', function() {
                    sidebar.classList.remove('mobile-open');
                    sidebarOverlay.classList.remove('show');
                });
            }

            // 添加滑动手势支持（移动端）
            let touchStartX = 0;
            let touchStartY = 0;
            let isSwiping = false;

            function handleTouchStart(e) {
                if (!isMobile()) return;
                touchStartX = e.touches[0].clientX;
                touchStartY = e.touches[0].clientY;
                isSwiping = true;
            }

            function handleTouchMove(e) {
                if (!isMobile() || !isSwiping) return;

                const touchCurrentX = e.touches[0].clientX;
                const touchCurrentY = e.touches[0].clientY;
                const deltaX = touchCurrentX - touchStartX;
                const deltaY = touchCurrentY - touchStartY;

                // 只处理水平滑动
                if (Math.abs(deltaY) > Math.abs(deltaX)) {
                    isSwiping = false;
                    return;
                }

                // 从左边缘向右滑动打开侧边栏
                if (touchStartX < 20 && deltaX > 50 && !sidebar.classList.contains('mobile-open')) {
                    e.preventDefault();
                    sidebar.classList.add('mobile-open');
                    if (sidebarOverlay) {
                        sidebarOverlay.classList.add('show');
                    }
                    isSwiping = false;
                }

                // 在侧边栏上向左滑动关闭
                if (sidebar.classList.contains('mobile-open') && deltaX < -50) {
                    e.preventDefault();
                    sidebar.classList.remove('mobile-open');
                    if (sidebarOverlay) {
                        sidebarOverlay.classList.remove('show');
                    }
                    isSwiping = false;
                }
            }

            function handleTouchEnd() {
                isSwiping = false;
            }

            // 添加触摸事件监听器
            document.addEventListener('touchstart', handleTouchStart, { passive: false });
            document.addEventListener('touchmove', handleTouchMove, { passive: false });
            document.addEventListener('touchend', handleTouchEnd);

            // 窗口大小变化处理
            function handleResize() {
                if (isMobile()) {
                    // 移动端：关闭侧边栏并移除桌面端的折叠状态
                    sidebar.classList.remove('mobile-open', 'collapsed');
                    if (sidebarOverlay) {
                        sidebarOverlay.classList.remove('show');
                    }
                    if (mainContent) {
                        mainContent.classList.remove('sidebar-collapsed');
                    }
                } else {
                    // 桌面端：恢复保存的折叠状态
                    sidebar.classList.remove('mobile-open');
                    if (sidebarOverlay) {
                        sidebarOverlay.classList.remove('show');
                    }
                    loadSidebarState();
                }
            }

            // 添加窗口大小变化监听器
            let resizeTimeout;
            window.addEventListener('resize', function() {
                clearTimeout(resizeTimeout);
                resizeTimeout = setTimeout(handleResize, 150);
            });

            // 窗口大小改变时的处理
            window.addEventListener('resize', function() {
                if (!isMobile()) {
                    // 桌面端时移除移动端相关类
                    sidebar.classList.remove('mobile-open');
                    sidebarOverlay.classList.remove('show');

                    // 恢复桌面端状态
                    const isCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';
                    if (isCollapsed) {
                        sidebar.classList.add('collapsed');
                        mainContent.classList.add('sidebar-collapsed');
                    } else {
                        sidebar.classList.remove('collapsed');
                        mainContent.classList.remove('sidebar-collapsed');
                    }
                } else {
                    // 移动端时移除桌面端状态
                    sidebar.classList.remove('collapsed');
                    mainContent.classList.remove('sidebar-collapsed');
                }
            });

            // 更新导航链接活动状态
            function updateNavActive(activeTab) {
                document.querySelectorAll('.nav-link').forEach(link => {
                    link.classList.remove('active');
                });
                const activeLink = document.querySelector(`[data-tab="${activeTab}"]`);
                if (activeLink) {
                    activeLink.classList.add('active');
                }

                // 移动端点击导航后自动关闭侧边栏
                if (isMobile()) {
                    sidebar.classList.remove('mobile-open');
                    sidebarOverlay.classList.remove('show');
                }
            }

            // 监听标签页切换，更新侧边栏活动状态
            window.updateSidebarActive = updateNavActive;

            // 为导航链接添加点击事件
            document.querySelectorAll('.nav-link').forEach(link => {
                link.addEventListener('click', function() {
                    const tab = this.getAttribute('data-tab');
                    if (tab) {
                        updateNavActive(tab);
                    }
                });
            });
        }

        // 页面加载完成后初始化侧边栏
        document.addEventListener('DOMContentLoaded', function() {
            initSidebar();
        });
    </script>
</body>
</html>
