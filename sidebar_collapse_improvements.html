<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>左侧导航菜单栏折叠功能改进</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 2.2em;
        }

        .feature-section {
            margin-bottom: 40px;
            padding: 25px;
            border: 1px solid #e0e0e0;
            border-radius: 12px;
            background: #f9f9f9;
        }

        .feature-title {
            font-size: 1.3em;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }

        .feature-title::before {
            content: "✅";
            margin-right: 12px;
            font-size: 1.2em;
        }

        .feature-details {
            margin-left: 35px;
        }

        .feature-details li {
            margin-bottom: 10px;
            color: #555;
            line-height: 1.6;
        }

        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            overflow-x: auto;
            margin: 15px 0;
        }

        .code-title {
            background: #4a5568;
            color: white;
            padding: 10px 20px;
            margin: -20px -20px 20px -20px;
            border-radius: 8px 8px 0 0;
            font-weight: 600;
        }

        .highlight {
            background: rgba(72, 187, 120, 0.3);
            padding: 2px 6px;
            border-radius: 4px;
            color: #2d5a3d;
            font-weight: 600;
        }

        .demo-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px;
            border-radius: 12px;
            margin: 30px 0;
        }

        .demo-section h3 {
            margin-top: 0;
            font-size: 1.4em;
        }

        .demo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .demo-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 8px;
            backdrop-filter: blur(10px);
        }

        .demo-item h4 {
            margin-top: 0;
            color: #f0f8ff;
        }

        .summary {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 25px;
            border-radius: 12px;
            margin-top: 30px;
        }

        .summary h3 {
            margin-top: 0;
            font-size: 1.4em;
        }

        .icon-demo {
            display: inline-block;
            font-size: 1.5em;
            margin: 0 10px;
            padding: 8px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 6px;
        }

        @media (max-width: 768px) {
            .container {
                padding: 20px;
                margin: 10px;
            }
            
            h1 {
                font-size: 1.8em;
            }
            
            .demo-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎛️ 左侧导航菜单栏折叠功能完善</h1>

        <div class="feature-section">
            <div class="feature-title">1. 折叠状态显示优化</div>
            <div class="feature-details">
                <ul>
                    <li><strong>图标显示</strong>：折叠时只显示菜单项的emoji图标，隐藏文字标签</li>
                    <li><strong>展开显示</strong>：展开时同时显示图标和文字标签</li>
                    <li><strong>视觉反馈</strong>：图标在悬停时有缩放动画效果</li>
                    <li><strong>布局调整</strong>：折叠时菜单项居中显示，展开时左对齐</li>
                </ul>
                
                <div class="code-block">
                    <div class="code-title">CSS 折叠状态样式</div>
                    <pre>.sidebar.collapsed .nav-text {
    <span class="highlight">opacity: 0;</span>
    <span class="highlight">pointer-events: none;</span>
}

.sidebar.collapsed .nav-link {
    <span class="highlight">justify-content: center;</span>
    padding: var(--spacing-md);
    border-radius: var(--radius-lg);
}</pre>
                </div>
            </div>
        </div>

        <div class="feature-section">
            <div class="feature-title">2. 切换机制改进</div>
            <div class="feature-details">
                <ul>
                    <li><strong>直观图标</strong>：使用箭头图标 <span class="icon-demo">◀</span> 替代汉堡菜单</li>
                    <li><strong>旋转动画</strong>：折叠时图标旋转180度，提供清晰的状态指示</li>
                    <li><strong>点击反馈</strong>：添加点击波纹效果和缩放动画</li>
                    <li><strong>悬停效果</strong>：鼠标悬停时按钮有缩放和发光效果</li>
                </ul>

                <div class="code-block">
                    <div class="code-title">改进的折叠按钮样式</div>
                    <pre>.toggle-icon {
    transition: transform var(--transition-smooth);
    <span class="highlight">display: inline-block;</span>
}

.sidebar.collapsed .toggle-icon {
    <span class="highlight">transform: rotate(180deg);</span>
}

.sidebar-toggle:hover .toggle-icon {
    <span class="highlight">transform: scale(1.1);</span>
}</pre>
                </div>
            </div>
        </div>

        <div class="feature-section">
            <div class="feature-title">3. 动画效果增强</div>
            <div class="feature-details">
                <ul>
                    <li><strong>平滑过渡</strong>：所有状态变化都有流畅的CSS过渡动画</li>
                    <li><strong>点击反馈</strong>：按钮点击时有波纹扩散效果</li>
                    <li><strong>图标动画</strong>：切换时图标有临时放大效果</li>
                    <li><strong>布局动画</strong>：侧边栏宽度变化有平滑过渡</li>
                </ul>

                <div class="code-block">
                    <div class="code-title">点击波纹效果实现</div>
                    <pre>.sidebar-toggle::before {
    content: '';
    position: absolute;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    <span class="highlight">transition: width 0.3s ease, height 0.3s ease;</span>
}

.sidebar-toggle:active::before {
    <span class="highlight">width: 40px;</span>
    <span class="highlight">height: 40px;</span>
}</pre>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h3>🎯 功能演示</h3>
            <div class="demo-grid">
                <div class="demo-item">
                    <h4>桌面端折叠</h4>
                    <p>• 点击 ◀ 按钮切换折叠状态</p>
                    <p>• 折叠时宽度从220px变为60px</p>
                    <p>• 只显示菜单图标：🎭 👥 📝 ⚡ 📋 ⚙️</p>
                </div>
                <div class="demo-item">
                    <h4>移动端菜单</h4>
                    <p>• 从左边缘向右滑动打开菜单</p>
                    <p>• 在菜单上向左滑动关闭</p>
                    <p>• 点击遮罩层关闭菜单</p>
                </div>
                <div class="demo-item">
                    <h4>状态保持</h4>
                    <p>• 折叠状态自动保存到localStorage</p>
                    <p>• 下次打开应用时恢复相同状态</p>
                    <p>• 移动端和桌面端分别处理</p>
                </div>
                <div class="demo-item">
                    <h4>响应式适配</h4>
                    <p>• 768px以下自动切换为移动端模式</p>
                    <p>• 窗口大小变化时自动调整</p>
                    <p>• 触摸友好的大按钮设计</p>
                </div>
            </div>
        </div>

        <div class="feature-section">
            <div class="feature-title">4. 状态保持功能</div>
            <div class="feature-details">
                <ul>
                    <li><strong>本地存储</strong>：使用localStorage保存用户的折叠偏好</li>
                    <li><strong>错误处理</strong>：添加try-catch确保存储操作的稳定性</li>
                    <li><strong>智能恢复</strong>：页面加载时自动恢复上次的折叠状态</li>
                    <li><strong>平台区分</strong>：移动端和桌面端使用不同的处理逻辑</li>
                </ul>

                <div class="code-block">
                    <div class="code-title">状态保持实现</div>
                    <pre>function saveSidebarState(collapsed) {
    try {
        <span class="highlight">localStorage.setItem('sidebarCollapsed', collapsed);</span>
        console.log('侧边栏状态已保存:', collapsed ? '折叠' : '展开');
    } catch (error) {
        console.warn('保存侧边栏状态失败:', error);
    }
}</pre>
                </div>
            </div>
        </div>

        <div class="feature-section">
            <div class="feature-title">5. 响应式设计优化</div>
            <div class="feature-details">
                <ul>
                    <li><strong>移动端优化</strong>：增大触摸区域，优化手势操作</li>
                    <li><strong>滑动手势</strong>：支持从左边缘滑动打开菜单</li>
                    <li><strong>遮罩层</strong>：添加模糊背景效果</li>
                    <li><strong>自适应布局</strong>：窗口大小变化时自动调整模式</li>
                </ul>
            </div>
        </div>

        <div class="summary">
            <h3>🎉 改进总结</h3>
            <p>通过以上改进，左侧导航菜单栏现在具备了完整的折叠功能：</p>
            <ul>
                <li><strong>直观的视觉反馈</strong>：使用箭头图标和旋转动画</li>
                <li><strong>流畅的交互体验</strong>：平滑的过渡动画和点击反馈</li>
                <li><strong>智能的状态管理</strong>：自动保存和恢复用户偏好</li>
                <li><strong>完善的响应式支持</strong>：桌面端和移动端的最佳体验</li>
                <li><strong>增强的移动端功能</strong>：滑动手势和触摸优化</li>
            </ul>
            <p>这些改进显著提升了用户体验，使导航菜单更加现代化和易用。</p>
        </div>
    </div>
</body>
</html>
