"""
角色CV分配工具 - 配置管理处理器

配置管理处理器，负责应用配置的加载、保存和验证。
基于项目分层架构设计，作为API层与服务层之间的桥梁。

主要功能：
- 加载和保存JSON格式的配置文件
- 提供默认配置
- 配置验证和合并
- 配置备份和恢复

设计特点：
- 支持配置文件的自动创建
- 提供配置验证机制
- 支持配置的增量更新
- 包含配置备份功能
"""

import json
import shutil
import time
from pathlib import Path
from typing import Dict, Any, Optional

# 导入服务层
from ...services.config_service import ConfigService

# 创建配置服务实例
_config_service = ConfigService()

def get_default_config() -> Dict[str, Any]:
    """
    获取默认配置。
    
    Returns:
        Dict[str, Any]: 默认配置字典
    """
    return {
        "API": {
            "base_url": "https://www.gstudios.com.cn/story_v2/api",
            "default_token": "",
            "enable_real_assignment": "false"
        },
        "BOOK": {
            "default_book_id": "33524"
        },
        "EXCEL": {
            "character_sheet_name": "角色",
            "character_column_index": "0",
            "cv_column_index": "6",
            "price_column_index": "2",
            "character_column_keyword": "角色名称",
            "cv_column_keyword": "主播",
            "price_column_keyword": "主播价格",
            "header_row": "1"
        },
        "FILES": {
            "cv_config_file": "config/cv_nicknames.json",
            "last_excel_directory": ""
        },
        "app": {
            "name": "角色CV分配工具",
            "version": "0.1.0",
            "theme": "light",
            "language": "zh-CN",
            "auto_save": True,
            "check_updates": True,
        },
        "advanced": {
            "debug_mode": False,
            "log_level": "INFO",
            "max_log_files": 5,
            "backup_count": 3,
        },
        "user_preferences": {
            "first_run_completed": False,
            "last_used": None,
            "usage_count": 0,
        }
    }


def load_config(config_path: str) -> Dict[str, Any]:
    """
    从文件加载配置。
    
    Args:
        config_path (str): 配置文件路径
    
    Returns:
        Dict[str, Any]: 配置字典
    
    Raises:
        FileNotFoundError: 配置文件不存在
        json.JSONDecodeError: 配置文件格式错误
    """
    config_file = Path(config_path)
    
    if not config_file.exists():
        # 如果配置文件不存在，创建默认配置
        default_config = get_default_config()
        save_config(config_path, default_config)
        return default_config
    
    try:
        # 使用ConfigService加载配置
        config = _config_service.get_all_config()
        
        # 合并默认配置，确保所有必要的键都存在
        merged_config = _merge_configs(get_default_config(), config)
        
        # 如果配置有更新，保存合并后的配置
        if merged_config != config:
            save_config(config_path, merged_config)
        
        return merged_config
    
    except json.JSONDecodeError as e:
        raise json.JSONDecodeError(f"配置文件格式错误: {e}", e.doc, e.pos)
    except Exception as e:
        raise Exception(f"加载配置文件失败: {e}")


def save_config(config_path: str, config: Dict[str, Any], create_backup: bool = True) -> Dict[str, Any]:
    """
    保存配置到文件。
    
    Args:
        config_path (str): 配置文件路径
        config (Dict[str, Any]): 要保存的配置
        create_backup (bool): 是否创建备份
    
    Returns:
        Dict[str, Any]: 操作结果
    """
    try:
        config_file = Path(config_path)
        
        # 创建目录（如果不存在）
        config_file.parent.mkdir(parents=True, exist_ok=True)
        
        # 创建备份（如果文件存在且需要备份）
        backup_path = None
        if create_backup and config_file.exists():
            backup_path = _create_config_backup(config_path)
        
        # 验证配置
        validation_result = validate_config(config)
        if not validation_result["valid"]:
            return {
                "success": False,
                "error": "配置验证失败",
                "validation_errors": validation_result["errors"],
                "message": "保存配置失败：配置数据无效"
            }
        
        # 使用ConfigService更新配置
        _config_service.update_config(config)
        
        return {
            "success": True,
            "config_path": config_path,
            "backup_path": backup_path,
            "message": "配置保存成功"
        }
    
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "message": f"保存配置失败: {e}"
        }


def validate_config(config: Dict[str, Any]) -> Dict[str, Any]:
    """
    验证配置的有效性。
    
    Args:
        config (Dict[str, Any]): 要验证的配置
    
    Returns:
        Dict[str, Any]: 验证结果
    """
    errors = []
    warnings = []
    
    try:
        # 检查基本结构
        required_sections = ["API", "EXCEL", "FILES"]
        for section in required_sections:
            if section not in config:
                errors.append(f"缺少必需的配置节: {section}")
        
        # 验证API节
        if "API" in config:
            api_config = config["API"]
            if not isinstance(api_config.get("base_url"), str):
                errors.append("API.base_url 必须是字符串")
        
        # 验证EXCEL节
        if "EXCEL" in config:
            excel_config = config["EXCEL"]
            if not isinstance(excel_config.get("character_column_keyword"), str):
                errors.append("EXCEL.character_column_keyword 必须是字符串")
        
        # 验证advanced节（如果存在）
        if "advanced" in config:
            advanced_config = config["advanced"]
            valid_log_levels = ["DEBUG", "INFO", "WARNING", "ERROR"]
            if advanced_config.get("log_level") not in valid_log_levels:
                warnings.append(f"advanced.log_level 应该是 {valid_log_levels} 中的一个")
        
        is_valid = len(errors) == 0
        
        return {
            "valid": is_valid,
            "errors": errors,
            "warnings": warnings,
            "message": "配置验证完成" if is_valid else "配置验证失败"
        }
    
    except Exception as e:
        return {
            "valid": False,
            "errors": [f"验证过程中出错: {e}"],
            "warnings": [],
            "message": "配置验证失败"
        }


def _merge_configs(default_config: Dict[str, Any], user_config: Dict[str, Any]) -> Dict[str, Any]:
    """
    合并默认配置和用户配置。
    
    Args:
        default_config (Dict[str, Any]): 默认配置
        user_config (Dict[str, Any]): 用户配置
    
    Returns:
        Dict[str, Any]: 合并后的配置
    """
    merged = default_config.copy()
    
    for key, value in user_config.items():
        if key in merged and isinstance(merged[key], dict) and isinstance(value, dict):
            # 递归合并字典
            merged[key] = _merge_configs(merged[key], value)
        else:
            # 直接覆盖
            merged[key] = value
    
    return merged


def _create_config_backup(config_path: str) -> str:
    """
    创建配置文件的备份。
    
    Args:
        config_path (str): 配置文件路径
    
    Returns:
        str: 备份文件路径
    """
    config_file = Path(config_path)
    timestamp = time.strftime("%Y%m%d_%H%M%S")
    backup_path = f"{config_file}.{timestamp}.bak"
    
    shutil.copy2(config_file, backup_path)
    
    return backup_path


def restore_config_from_backup(backup_path: str, config_path: str) -> Dict[str, Any]:
    """
    从备份恢复配置。
    
    Args:
        backup_path (str): 备份文件路径
        config_path (str): 目标配置文件路径
    
    Returns:
        Dict[str, Any]: 操作结果
    """
    try:
        backup_file = Path(backup_path)
        config_file = Path(config_path)
        
        if not backup_file.exists():
            return {
                "success": False,
                "error": "备份文件不存在",
                "message": "恢复配置失败：备份文件不存在"
            }
        
        # 创建当前配置的备份
        if config_file.exists():
            current_backup = _create_config_backup(config_path)
        else:
            current_backup = None
        
        # 复制备份文件到配置文件
        shutil.copy2(backup_file, config_file)
        
        # 重新加载配置
        config = load_config(config_path)
        
        return {
            "success": True,
            "config": config,
            "current_backup": current_backup,
            "message": "配置已从备份恢复"
        }
    
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "message": f"恢复配置失败: {e}"
        }


def list_config_backups(config_path: str) -> Dict[str, Any]:
    """
    列出配置文件的所有备份。
    
    Args:
        config_path (str): 配置文件路径
    
    Returns:
        Dict[str, Any]: 备份列表
    """
    try:
        config_file = Path(config_path)
        config_dir = config_file.parent
        config_name = config_file.stem
        
        # 查找备份文件
        backup_pattern = f"{config_name}.backup.*.json"
        backup_files = list(config_dir.glob(backup_pattern))
        
        backups = []
        for backup_file in backup_files:
            try:
                # 从文件名提取时间戳
                parts = backup_file.stem.split('.')
                if len(parts) >= 3:
                    timestamp = int(parts[-1])
                    backup_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(timestamp))
                    
                    backups.append({
                        "path": str(backup_file),
                        "timestamp": timestamp,
                        "backup_time": backup_time,
                        "size": backup_file.stat().st_size,
                    })
            except (ValueError, OSError):
                # 跳过无效的备份文件
                continue
        
        # 按时间戳排序（最新的在前）
        backups.sort(key=lambda x: x["timestamp"], reverse=True)
        
        return {
            "success": True,
            "backups": backups,
            "total_backups": len(backups),
            "message": f"找到 {len(backups)} 个配置备份"
        }
    
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "message": f"列出备份失败: {e}"
        }


