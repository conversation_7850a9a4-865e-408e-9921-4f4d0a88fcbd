"""
角色CV分配工具 - 核心API类

核心API类，提供前端JavaScript可以调用的所有方法。
基于 Augment-Code-Free 的设计模式，提供统一的接口和错误处理。

主要功能：
- 书籍、角色、CV数据管理
- Excel文件处理
- CV简名管理
- 角色CV分配任务
- 配置管理
- 错误处理和日志记录

设计模式：
- 门面模式：为复杂的后端操作提供简化接口
- 策略模式：根据不同条件选择不同的处理策略
- 单例模式：确保API实例的唯一性（可选）
"""

import json
import os
import traceback
import webbrowser
from pathlib import Path
from typing import Dict, Any, Optional

# 导入处理器
from .handlers import (
    load_config,
    save_config,
    get_default_config,
    validate_config,
)

# 导入工具模块
from ..utils.paths import (
    get_home_dir,
    get_app_data_dir,
    get_config_dir,
)
from ..utils.helpers import (
    format_success_response,
    format_error_response,
    validate_params,
)

# 导入服务
from ..services.api_service import APIService
from ..services.cv_service import CVService
from ..services.config_service import ConfigService

# 导入处理器
from .handlers.book_handler import get_books, get_characters, get_cvs, get_book_info
from .handlers.file_handler import (
    upload_excel_file, parse_excel_file, validate_excel_file, get_excel_preview, cleanup_temp_file,
    validate_excel_file_from_data, parse_excel_mapping_from_data, preview_excel_columns_from_data,
    validate_excel_file_from_path, parse_excel_mapping_from_path, preview_excel_columns_from_path,
    select_excel_file_with_pywebview
)
from .handlers.cv_handler import get_cv_nicknames, save_cv_nicknames, match_cvs, update_cv_nickname_mapping, delete_cv_nickname_mapping
from .handlers.assignment_handler import start_assignment, get_assignment_progress, get_assignment_result


class AssignCvToCharactersAPI:
    """
    角色CV分配工具的核心API类。

    这个类提供了前端可以调用的所有方法，
    用于执行各种业务操作和系统交互。
    """

    def __init__(self):
        """初始化API实例。"""
        self.status = "initializing"
        self.app_name = "角色CV分配工具"
        self.version = "0.1.0"
        self._config_dir = self._get_config_dir()
        # 使用config目录下的config.json文件
        self._config_file = Path("config") / "config.json"
        self._first_run_file = self._config_dir / ".first_run"

        print(f"🚀 开始初始化 {self.app_name}...")

        # 第一阶段：初始化基础服务
        print("📋 第一阶段：初始化基础服务...")
        self.config_service = ConfigService()
        self.cv_service = CVService()

        # 第二阶段：加载和验证配置
        print("⚙️  第二阶段：加载和验证配置...")
        # 使用处理器层的load_config函数加载配置
        try:
            self.config = load_config(str(self._config_file))
        except Exception as e:
            print(f"❌ 配置加载失败: {e}")
            # 使用默认配置
            self.config = get_default_config()

        # 第三阶段：应用配置到服务
        print("🔧 第三阶段：应用配置到服务...")
        self._apply_config_to_services()

        # 第四阶段：加载扩展数据
        print("📚 第四阶段：加载扩展数据...")
        self._load_cv_nicknames()

        # 第五阶段：验证系统状态
        print("✅ 第五阶段：验证系统状态...")
        self._validate_system_state()

        self.status = "ready"
        print(f"🎉 {self.app_name} 初始化完成，系统已就绪！")

    def _apply_config_to_services(self):
        """将配置应用到各个服务中"""
        try:
            # 应用API配置
            api_config = self.config.get("API", {})
            saved_token = api_config.get("default_token", "")

            if saved_token:
                print(f"🔑 发现保存的API Token: {saved_token[:8]}...")
                # 初始化API服务并设置Token
                self.api_service = APIService(token=saved_token)
                # 立即应用Token到API服务
                self.api_service.set_token(saved_token)
                print("✅ API Token已应用到API服务")
            else:
                print("⚠️  未找到保存的API Token")
                self.api_service = APIService()

            # 应用其他配置到相应服务
            # 这里可以添加更多配置应用逻辑

        except Exception as e:
            print(f"❌ 配置应用失败: {e}")
            raise

    def _validate_system_state(self):
        """验证系统状态"""
        try:
            # 验证配置完整性
            required_sections = ["API", "EXCEL", "FILES"]
            for section in required_sections:
                if section not in self.config:
                    print(f"⚠️  配置缺少 {section} 节点")
                else:
                    print(f"✅ {section} 配置已加载")

            # 验证API服务状态 - 检查多种可能的Token存储方式
            api_token_configured = False
            if hasattr(self.api_service, 'token') and self.api_service.token:
                api_token_configured = True
                print(f"✅ API服务已配置Token: {self.api_service.token[:8]}...")
            elif hasattr(self.api_service, '_token') and self.api_service._token:
                api_token_configured = True
                print(f"✅ API服务已配置Token: {self.api_service._token[:8]}...")
            else:
                # 检查配置中的Token
                api_config = self.config.get("API", {})
                if api_config.get("default_token"):
                    api_token_configured = True
                    print(f"✅ 配置中已设置Token: {api_config['default_token'][:8]}...")
                else:
                    print("⚠️  API服务未配置Token")

            # 验证CV服务状态
            cv_nicknames_loaded = False
            if hasattr(self.cv_service, 'nickname_map') and self.cv_service.nickname_map:
                nickname_count = len(self.cv_service.nickname_map)
                cv_nicknames_loaded = True
                print(f"✅ CV简名映射已加载 ({nickname_count}个映射)")
            else:
                print("⚠️  CV简名映射未加载")

            # 存储验证结果供get_system_ready_state使用
            self._validation_results = {
                'api_token_configured': api_token_configured,
                'cv_nicknames_loaded': cv_nicknames_loaded
            }

        except Exception as e:
            print(f"❌ 系统状态验证失败: {e}")
            # 不抛出异常，允许系统继续运行
            self._validation_results = {
                'api_token_configured': False,
                'cv_nicknames_loaded': False
            }

    def _get_config_dir(self) -> Path:
        """
        获取应用配置目录。

        Returns:
            Path: 配置目录路径
        """
        try:
            config_dir = get_config_dir(self.app_name.lower().replace(" ", "_"))
            config_dir.mkdir(exist_ok=True)
            return config_dir
        except Exception:
            # 回退到当前目录
            return Path(".")

    def _load_initial_config(self) -> Dict[str, Any]:
        """
        加载初始配置。

        Returns:
            Dict[str, Any]: 配置字典
        """
        try:
            import json
            if self._config_file.exists():
                with open(self._config_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            else:
                # 如果配置文件不存在，返回默认的角色CV分配工具配置
                default_config = {
                    "API": {
                        "base_url": "https://www.gstudios.com.cn/story_v2/api",
                        "default_token": "",
                        "enable_real_assignment": "false"
                    },
                    "BOOK": {
                        "default_book_id": "33524"
                    },
                    "EXCEL": {
                        "character_sheet_name": "角色",
                        "character_column_index": "0",
                        "cv_column_index": "6",
                        "price_column_index": "2",
                        "character_column_keyword": "角色名称",
                        "cv_column_keyword": "主播",
                        "price_column_keyword": "主播价格",
                        "header_row": "1"
                    },
                    "FILES": {
                        "cv_config_file": "config/cv_nicknames.json",
                        "last_excel_directory": ""
                    }
                }
                # 创建默认配置文件
                with open(self._config_file, 'w', encoding='utf-8') as f:
                    json.dump(default_config, f, indent=4, ensure_ascii=False)
                return default_config
        except Exception as e:
            print(f"加载配置文件失败: {e}")
            # 返回最小配置
            return {
                "API": {"base_url": "", "default_token": "", "enable_real_assignment": "false"},
                "EXCEL": {"character_column_keyword": "", "cv_column_keyword": "", "price_column_keyword": "", "header_row": ""},
                "FILES": {"cv_config_file": "", "last_excel_directory": ""}
            }

    def _load_cv_nicknames(self):
        """加载CV简名映射"""
        try:
            result = get_cv_nicknames(self.config_service)
            if result["success"]:
                cv_nicknames = result["data"]["cv_nicknames"]
                self.cv_service.update_nickname_map(cv_nicknames)
        except Exception as e:
            print(f"加载CV简名映射失败: {e}")
    
    # ==================== 基础API方法 ====================
    
    def get_status(self, *args, **kwargs) -> Dict[str, Any]:
        """
        获取API状态。

        Args:
            *args: pywebview传递的额外参数
            **kwargs: pywebview传递的额外关键字参数

        Returns:
            Dict[str, Any]: API状态信息
        """
        return format_success_response(
            data={"status": self.status},
            message="API运行正常"
        )

    def get_system_ready_state(self, *args, **kwargs) -> Dict[str, Any]:
        """
        获取系统就绪状态，包含详细的初始化信息。

        Returns:
            Dict[str, Any]: 系统就绪状态信息
        """
        try:
            # 使用验证结果或重新检查
            if hasattr(self, '_validation_results'):
                api_token_configured = self._validation_results['api_token_configured']
                cv_nicknames_loaded = self._validation_results['cv_nicknames_loaded']
            else:
                # 重新检查系统各组件状态
                api_token_configured = bool(
                    (hasattr(self.api_service, 'token') and self.api_service.token) or
                    (hasattr(self.api_service, '_token') and self.api_service._token) or
                    (self.config.get("API", {}).get("default_token"))
                )

                cv_nicknames_loaded = bool(
                    hasattr(self.cv_service, 'nickname_map') and
                    self.cv_service.nickname_map and
                    len(self.cv_service.nickname_map) > 0
                )

            config_loaded = bool(self.config and len(self.config) > 0)

            # 计算就绪程度
            ready_components = sum([
                self.status == "ready",
                api_token_configured,
                config_loaded,
                cv_nicknames_loaded
            ])

            total_components = 4
            ready_percentage = (ready_components / total_components) * 100

            return format_success_response(
                data={
                    "system_status": self.status,
                    "ready_percentage": ready_percentage,
                    "is_fully_ready": ready_components == total_components,
                    "components": {
                        "api_initialized": self.status == "ready",
                        "api_token_configured": api_token_configured,
                        "config_loaded": config_loaded,
                        "cv_nicknames_loaded": cv_nicknames_loaded
                    },
                    "config_summary": {
                        "api_token": "已配置" if api_token_configured else "未配置",
                        "config_sections": list(self.config.keys()) if config_loaded else [],
                        "cv_nickname_count": len(self.cv_service.nickname_map) if hasattr(self.cv_service, 'nickname_map') and self.cv_service.nickname_map else 0
                    }
                },
                message=f"系统就绪度: {ready_percentage:.0f}%"
            )
        except Exception as e:
            return format_error_response(
                error=str(e),
                message="获取系统状态失败"
            )
    
    def get_app_info(self, *args, **kwargs) -> Dict[str, Any]:
        """
        获取应用程序信息。

        Args:
            *args: pywebview传递的额外参数
            **kwargs: pywebview传递的额外关键字参数

        Returns:
            Dict[str, Any]: 应用信息
        """
        try:
            app_info = {
                "name": self.app_name,
                "version": self.version,
                "config_dir": str(self._config_dir),
                "is_first_run": not self._first_run_file.exists(),
            }
            
            return format_success_response(
                data=app_info,
                message="应用信息获取成功"
            )
        except Exception as e:
            return format_error_response(
                error=str(e),
                message="获取应用信息失败"
            )
    
    def get_system_info(self, *args, **kwargs) -> Dict[str, Any]:
        """
        获取系统信息。

        Args:
            *args: pywebview传递的额外参数
            **kwargs: pywebview传递的额外关键字参数

        Returns:
            Dict[str, Any]: 系统信息
        """
        try:
            system_info = {
                "home_dir": get_home_dir(),
                "app_data_dir": get_app_data_dir(),
                "config_dir": str(self._config_dir),
                "platform": os.name,
            }
            
            return format_success_response(
                data=system_info,
                message="系统信息获取成功"
            )
        except Exception as e:
            return format_error_response(
                error=str(e),
                message="获取系统信息失败"
            )
    
    # ==================== 配置管理方法 ====================
    
    def get_config(self, *args, **kwargs) -> Dict[str, Any]:
        """
        获取当前配置。

        Args:
            *args: pywebview传递的额外参数
            **kwargs: pywebview传递的额外关键字参数

        Returns:
            Dict[str, Any]: 当前配置
        """
        try:
            return format_success_response(
                data=self.config,
                message="配置获取成功"
            )
        except Exception as e:
            return format_error_response(
                error=str(e),
                message="获取配置失败"
            )

    def update_config(self, new_config: Dict[str, Any], *args, **kwargs) -> Dict[str, Any]:
        """
        更新配置。

        Args:
            new_config (Dict[str, Any]): 新的配置数据
            *args: pywebview传递的额外参数
            **kwargs: pywebview传递的额外关键字参数

        Returns:
            Dict[str, Any]: 更新结果
        """
        try:
            # 验证参数
            if not isinstance(new_config, dict):
                return format_error_response(
                    error="配置数据必须是字典格式",
                    message="配置更新失败"
                )
            
            # 合并配置
            self.config.update(new_config)

            # 使用处理器层的save_config函数保存配置
            result = save_config(str(self._config_file), self.config)
            
            if not result["success"]:
                return format_error_response(
                    error=result.get("error", "未知错误"),
                    message=result.get("message", "配置更新失败")
                )

            return format_success_response(
                data=self.config,
                message="配置更新成功"
            )
        except Exception as e:
            return format_error_response(
                error=str(e),
                message="更新配置失败"
            )

    def reset_config(self, *args, **kwargs) -> Dict[str, Any]:
        """
        重置配置为默认值。

        Args:
            *args: pywebview传递的额外参数
            **kwargs: pywebview传递的额外关键字参数

        Returns:
            Dict[str, Any]: 重置结果
        """
        try:
            # 使用处理器层的get_default_config函数获取默认配置
            self.config = get_default_config()

            # 使用处理器层的save_config函数保存配置
            result = save_config(str(self._config_file), self.config)
            
            if not result["success"]:
                return format_error_response(
                    error=result.get("error", "未知错误"),
                    message=result.get("message", "配置重置失败")
                )

            return format_success_response(
                data=self.config,
                message="配置重置成功"
            )
        except Exception as e:
            return format_error_response(
                error=str(e),
                message="重置配置失败"
            )

    def backup_config(self, *args, **kwargs) -> Dict[str, Any]:
        """
        创建配置备份。

        Args:
            *args: pywebview传递的额外参数
            **kwargs: pywebview传递的额外关键字参数

        Returns:
            Dict[str, Any]: 备份结果
        """
        try:
            # 使用处理器层的_create_config_backup函数创建备份
            from .handlers.config_handler import _create_config_backup
            
            backup_path = _create_config_backup(str(self._config_file))
            
            return format_success_response(
                data={"backup_path": backup_path},
                message="配置备份成功"
            )
        except Exception as e:
            return format_error_response(
                error=str(e),
                message="创建配置备份失败"
            )

    def restore_config(self, backup_path: str, *args, **kwargs) -> Dict[str, Any]:
        """
        从备份恢复配置。

        Args:
            backup_path (str): 备份文件路径
            *args: pywebview传递的额外参数
            **kwargs: pywebview传递的额外关键字参数

        Returns:
            Dict[str, Any]: 恢复结果
        """
        try:
            # 使用处理器层的restore_config_from_backup函数恢复配置
            from .handlers.config_handler import restore_config_from_backup
            
            result = restore_config_from_backup(backup_path, str(self._config_file))
            
            if not result["success"]:
                return format_error_response(
                    error=result.get("error", "未知错误"),
                    message=result.get("message", "恢复配置失败")
                )
            
            # 更新内存中的配置
            self.config = result["config"]
            
            # 重新应用配置到服务
            self._apply_config_to_services()
            
            return format_success_response(
                data={"config": self.config, "backup_info": result},
                message="配置已从备份恢复"
            )
        except Exception as e:
            return format_error_response(
                error=str(e),
                message="恢复配置失败"
            )
    
    # ==================== 业务操作方法 ====================
    
    def example_operation(self, params: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        示例业务操作。
        
        Args:
            params (Dict[str, Any], optional): 操作参数
            
        Returns:
            Dict[str, Any]: 操作结果
        """
        try:
            # 参数验证
            if params is None:
                params = {}
            
            # 调用处理器
            result = example_operation(params)
            
            return format_success_response(
                data=result,
                message="示例操作执行成功"
            )
        except Exception as e:
            return format_error_response(
                error=str(e),
                message="示例操作执行失败",
                traceback=traceback.format_exc()
            )
    
    # ==================== 工具方法 ====================
    
    def open_external_link(self, url: str) -> Dict[str, Any]:
        """
        在默认浏览器中打开外部链接。
        
        Args:
            url (str): 要打开的URL
            
        Returns:
            Dict[str, Any]: 操作结果
        """
        try:
            # 验证URL
            if not url or not isinstance(url, str):
                return format_error_response(
                    error="URL不能为空且必须是字符串",
                    message="打开链接失败"
                )
            
            webbrowser.open(url)
            
            return format_success_response(
                data={"url": url},
                message=f"已在浏览器中打开: {url}"
            )
        except Exception as e:
            return format_error_response(
                error=str(e),
                message=f"打开链接失败: {url}"
            )
    
    def mark_first_run_complete(self, *args, **kwargs) -> Dict[str, Any]:
        """
        标记首次运行完成。

        Args:
            *args: pywebview传递的额外参数
            **kwargs: pywebview传递的额外关键字参数

        Returns:
            Dict[str, Any]: 操作结果
        """
        try:
            self._first_run_file.touch()
            
            return format_success_response(
                data={"marked": True},
                message="首次运行标记完成"
            )
        except Exception as e:
            return format_error_response(
                error=str(e),
                message="标记首次运行失败"
            )
    
    def is_first_run(self, *args, **kwargs) -> Dict[str, Any]:
        """
        检查是否为首次运行。

        Args:
            *args: pywebview传递的额外参数
            **kwargs: pywebview传递的额外关键字参数

        Returns:
            Dict[str, Any]: 检查结果
        """
        try:
            is_first = not self._first_run_file.exists()
            
            return format_success_response(
                data={"is_first_run": is_first},
                message="首次运行检查完成"
            )
        except Exception as e:
            return format_error_response(
                error=str(e),
                message="检查首次运行状态失败"
            )
    
    # ==================== 扩展方法区域 ====================
    # 在这里添加您的自定义业务方法
    
    def custom_method_template(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        自定义方法模板。
        复制这个方法并修改为您的具体业务逻辑。
        
        Args:
            params (Dict[str, Any]): 方法参数
            
        Returns:
            Dict[str, Any]: 操作结果
        """
        try:
            # 1. 参数验证
            required_params = ["param1", "param2"]  # 修改为您需要的参数
            validation_result = validate_params(params, required_params)
            if not validation_result["valid"]:
                return format_error_response(
                    error=validation_result["error"],
                    message="参数验证失败"
                )
            
            # 2. 执行业务逻辑
            # 在这里实现您的具体业务逻辑
            result = {
                "processed": True,
                "data": params,
                "timestamp": "2024-01-01T00:00:00Z"  # 示例数据
            }
            
            # 3. 返回成功结果
            return format_success_response(
                data=result,
                message="自定义操作执行成功"
            )
        except Exception as e:
            return format_error_response(
                error=str(e),
                message="自定义操作执行失败",
                traceback=traceback.format_exc()
            )

    # ==================== 角色CV分配工具专用API方法 ====================

    def get_api_token(self, *args, **kwargs) -> Dict[str, Any]:
        """
        获取当前保存的API Token

        Args:
            *args: pywebview传递的额外参数
            **kwargs: pywebview传递的额外关键字参数

        Returns:
            Dict[str, Any]: 当前Token信息
        """
        try:
            saved_token = self.config_service.get("API", "default_token", "")
            return format_success_response(
                data={"token": saved_token, "has_token": bool(saved_token)},
                message="获取API Token成功"
            )
        except Exception as e:
            return format_error_response(
                error=str(e),
                message="获取API Token失败"
            )

    def set_api_token(self, token: str, *args, **kwargs) -> Dict[str, Any]:
        """
        设置API Token

        Args:
            token (str): API Token
            *args: pywebview传递的额外参数
            **kwargs: pywebview传递的额外关键字参数

        Returns:
            Dict[str, Any]: 设置结果
        """
        try:
            self.api_service.set_token(token)
            self.config_service.set("API", "default_token", token)
            self.config_service.save()
            return format_success_response(
                data={"token": token},
                message="API Token设置成功"
            )
        except Exception as e:
            return format_error_response(
                error=str(e),
                message="设置API Token失败"
            )

    # ==================== 书籍和数据管理方法 ====================

    def get_books(self, status_filter: str = "unfinished", *args, **kwargs) -> Dict[str, Any]:
        """
        获取书籍列表

        Args:
            status_filter (str): 状态过滤器，默认为"unfinished"
            *args: pywebview传递的额外参数
            **kwargs: pywebview传递的额外关键字参数

        Returns:
            Dict[str, Any]: 书籍列表
        """
        return get_books(self.api_service, status_filter)

    def get_characters(self, book_id: str, *args, **kwargs) -> Dict[str, Any]:
        """
        获取角色列表

        Args:
            book_id (str): 书籍ID
            *args: pywebview传递的额外参数
            **kwargs: pywebview传递的额外关键字参数

        Returns:
            Dict[str, Any]: 角色列表
        """
        return get_characters(self.api_service, book_id)

    def get_cvs(self, book_id: str, *args, **kwargs) -> Dict[str, Any]:
        """
        获取CV列表

        Args:
            book_id (str): 书籍ID
            *args: pywebview传递的额外参数
            **kwargs: pywebview传递的额外关键字参数

        Returns:
            Dict[str, Any]: CV列表
        """
        return get_cvs(self.api_service, book_id)

    def get_book_info(self, book_id: str, *args, **kwargs) -> Dict[str, Any]:
        """
        获取书籍详细信息

        Args:
            book_id (str): 书籍ID
            *args: pywebview传递的额外参数
            **kwargs: pywebview传递的额外关键字参数

        Returns:
            Dict[str, Any]: 书籍详细信息
        """
        return get_book_info(self.api_service, book_id)

    # ==================== 文件处理方法 ====================

    def upload_excel_file(self, file_data: str, filename: str) -> Dict[str, Any]:
        """上传Excel文件"""
        return upload_excel_file(file_data, filename)

    def parse_excel_file(self, file_path: str) -> Dict[str, Any]:
        """解析Excel文件"""
        return parse_excel_file(file_path, self.config_service)

    def validate_excel_file(self, file_path: str, sheet_name: str = None) -> Dict[str, Any]:
        """验证Excel文件"""
        return validate_excel_file(file_path, sheet_name)

    def get_excel_preview(self, file_path: str, sheet_name: str, max_rows: int = 10) -> Dict[str, Any]:
        """获取Excel文件预览"""
        return get_excel_preview(file_path, sheet_name, max_rows)

    def cleanup_temp_file(self, file_path: str) -> Dict[str, Any]:
        """清理临时文件"""
        return cleanup_temp_file(file_path)

    def validate_excel_file_from_data(self, file_data: str, filename: str) -> Dict[str, Any]:
        """从Base64数据验证Excel文件"""
        return validate_excel_file_from_data(file_data, filename)

    def parse_excel_mapping(self, file_data: str, file_name: str, config: Dict[str, Any]) -> Dict[str, Any]:
        """从Base64数据解析Excel角色-CV映射"""
        return parse_excel_mapping_from_data(file_data, file_name, config)

    def preview_excel_columns(self, file_data: str, file_name: str, sheet_name: str, header_row: int = 0) -> Dict[str, Any]:
        """从Base64数据预览Excel列结构"""
        return preview_excel_columns_from_data(file_data, file_name, sheet_name, header_row)

    def validate_excel_file_from_path(self, file_path: str) -> Dict[str, Any]:
        """从文件路径验证Excel文件"""
        print(f"🌐 [API] validate_excel_file_from_path 调用: {file_path}")
        result = validate_excel_file_from_path(file_path)
        print(f"🌐 [API] validate_excel_file_from_path 返回: success={result.get('success')}")
        return result

    def parse_excel_mapping_from_path(self, file_path: str) -> Dict[str, Any]:
        """从文件路径解析Excel角色-CV映射（使用配置文件设置）"""
        print(f"🌐 [API] parse_excel_mapping_from_path 调用: 文件={file_path}")
        result = parse_excel_mapping_from_path(file_path, self.config_service)
        print(f"🌐 [API] parse_excel_mapping_from_path 返回: success={result.get('success')}, 数据量={len(result.get('data', []))}")
        return result

    def select_excel_file_dialog(self) -> Dict[str, Any]:
        """使用pywebview文件对话框选择Excel文件"""
        print(f"🌐 [API] select_excel_file_dialog 调用")
        result = select_excel_file_with_pywebview()
        print(f"🌐 [API] select_excel_file_dialog 返回: success={result.get('success')}")
        return result

    # ==================== CV管理方法 ====================

    def get_cv_nicknames(self, *args, **kwargs) -> Dict[str, Any]:
        """
        获取CV简名映射

        Args:
            *args: pywebview传递的额外参数
            **kwargs: pywebview传递的额外关键字参数

        Returns:
            Dict[str, Any]: CV简名映射
        """
        return get_cv_nicknames(self.config_service)

    def log_to_terminal(self, message: str, *args, **kwargs) -> Dict[str, Any]:
        """
        将前端日志输出到终端控制台

        Args:
            message (str): 要输出的日志消息
            *args: pywebview传递的额外参数
            **kwargs: pywebview传递的额外关键字参数

        Returns:
            Dict[str, Any]: 操作结果
        """
        try:
            import datetime
            timestamp = datetime.datetime.now().strftime("%H:%M:%S")
            print(f"[{timestamp}] 🌐 {message}")
            return format_success_response(
                data={"logged": True},
                message="日志已输出到终端"
            )
        except Exception as e:
            return format_error_response(
                error=str(e),
                message="终端日志输出失败"
            )

    def save_cv_nicknames(self, cv_nicknames: Dict[str, str]) -> Dict[str, Any]:
        """保存CV简名映射"""
        result = save_cv_nicknames(cv_nicknames, self.config_service)
        # 更新CV服务中的映射
        if result["success"]:
            self.cv_service.update_nickname_map(cv_nicknames)
        return result

    def update_cv_nickname_mapping(self, nickname: str, fullname: str) -> Dict[str, Any]:
        """更新单个CV简名映射"""
        result = update_cv_nickname_mapping(nickname, fullname, self.config_service)
        # 重新加载CV简名映射
        if result["success"]:
            self._load_cv_nicknames()
        return result

    def delete_cv_nickname_mapping(self, nickname: str) -> Dict[str, Any]:
        """删除CV简名映射"""
        result = delete_cv_nickname_mapping(nickname, self.config_service)
        # 重新加载CV简名映射
        if result["success"]:
            self._load_cv_nicknames()
        return result

    def match_cvs(self, character_cv_mapping: Dict[str, str], characters: list, cvs: list) -> Dict[str, Any]:
        """匹配角色和CV"""
        return match_cvs(character_cv_mapping, characters, cvs, self.cv_service)

    # ==================== 分配任务方法 ====================

    def start_assignment(self, book_id: str, character_cv_mapping: Dict[str, str],
                        enable_real_assignment: bool = False) -> Dict[str, Any]:
        """开始分配任务"""
        return start_assignment(
            book_id,
            character_cv_mapping,
            enable_real_assignment,
            self.api_service,
            self.cv_service
        )

    def get_assignment_progress(self, task_id: str) -> Dict[str, Any]:
        """获取分配任务进度"""
        return get_assignment_progress(task_id)

    def get_assignment_result(self, task_id: str) -> Dict[str, Any]:
        """获取分配任务结果"""
        return get_assignment_result(task_id)
